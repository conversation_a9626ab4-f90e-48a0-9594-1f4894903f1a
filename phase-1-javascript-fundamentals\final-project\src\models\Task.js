// Task Model Class
// Demonstrates: ES6 Classes, Private Fields, Enums, Date Handling

const { ValidationError, TaskError } = require('../utils/errors');
const { validateRequired } = require('../utils/validators');

/**
 * Task priority levels
 */
const PRIORITY_LEVELS = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent'
};

/**
 * Task status types
 */
const TASK_STATUS = {
    TODO: 'todo',
    IN_PROGRESS: 'in-progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

/**
 * Task class representing a user task
 * Demonstrates ES6 classes, private fields, and comprehensive validation
 */
class Task {
    // Private fields
    #id;
    #createdAt;
    #updatedAt;
    #completedAt;
    #history;

    /**
     * Create a new Task instance
     * @param {Object} taskData - Task data object
     * @param {string} taskData.title - Task title
     * @param {string} taskData.description - Task description
     * @param {string} taskData.userId - ID of the user who owns this task
     * @param {string} taskData.priority - Task priority level
     * @param {string} taskData.category - Task category
     * @param {Date} taskData.dueDate - Task due date (optional)
     */
    constructor(taskData) {
        // Validate input data
        this.#validateTaskData(taskData);
        
        // Initialize private fields
        this.#id = this.#generateId();
        this.#createdAt = new Date();
        this.#updatedAt = new Date();
        this.#completedAt = null;
        this.#history = [];
        
        // Public properties
        this.title = taskData.title.trim();
        this.description = taskData.description ? taskData.description.trim() : '';
        this.userId = taskData.userId;
        this.priority = taskData.priority || PRIORITY_LEVELS.MEDIUM;
        this.category = taskData.category || 'General';
        this.status = TASK_STATUS.TODO;
        this.dueDate = taskData.dueDate ? new Date(taskData.dueDate) : null;
        this.tags = taskData.tags || [];
        this.estimatedHours = taskData.estimatedHours || null;
        this.actualHours = null;

        // Record creation in history
        this.#addToHistory('created', 'Task created');
    }

    /**
     * Validate task data during construction
     * @param {Object} taskData - Task data to validate
     * @throws {ValidationError} If validation fails
     */
    #validateTaskData(taskData) {
        if (!taskData || typeof taskData !== 'object') {
            throw new ValidationError('Task data must be an object', 'taskData');
        }

        // Validate required fields
        if (!validateRequired(taskData.title)) {
            throw new ValidationError('Task title is required', 'title');
        }

        if (!validateRequired(taskData.userId)) {
            throw new ValidationError('User ID is required', 'userId');
        }

        // Validate title length
        if (taskData.title.trim().length < 3) {
            throw new ValidationError('Task title must be at least 3 characters long', 'title');
        }

        if (taskData.title.trim().length > 200) {
            throw new ValidationError('Task title must be less than 200 characters', 'title');
        }

        // Validate description length if provided
        if (taskData.description && taskData.description.length > 1000) {
            throw new ValidationError('Task description must be less than 1000 characters', 'description');
        }

        // Validate priority if provided
        if (taskData.priority && !Object.values(PRIORITY_LEVELS).includes(taskData.priority)) {
            throw new ValidationError(`Priority must be one of: ${Object.values(PRIORITY_LEVELS).join(', ')}`, 'priority');
        }

        // Validate due date if provided
        if (taskData.dueDate) {
            const dueDate = new Date(taskData.dueDate);
            if (isNaN(dueDate.getTime())) {
                throw new ValidationError('Invalid due date format', 'dueDate');
            }
        }

        // Validate estimated hours if provided
        if (taskData.estimatedHours !== undefined && taskData.estimatedHours !== null) {
            if (typeof taskData.estimatedHours !== 'number' || taskData.estimatedHours < 0) {
                throw new ValidationError('Estimated hours must be a positive number', 'estimatedHours');
            }
        }
    }

    /**
     * Generate a unique ID for the task
     * @returns {string} Unique identifier
     */
    #generateId() {
        const timestamp = Date.now().toString(36);
        const randomPart = Math.random().toString(36).substr(2, 9);
        return `task_${timestamp}_${randomPart}`;
    }

    /**
     * Add an entry to the task history
     * @param {string} action - Action performed
     * @param {string} details - Additional details
     */
    #addToHistory(action, details = '') {
        this.#history.push({
            timestamp: new Date(),
            action,
            details,
            status: this.status
        });

        // Keep only last 20 history entries
        if (this.#history.length > 20) {
            this.#history = this.#history.slice(-20);
        }
    }

    /**
     * Get task ID (read-only)
     * @returns {string} Task ID
     */
    get id() {
        return this.#id;
    }

    /**
     * Get creation date (read-only)
     * @returns {Date} Creation date
     */
    get createdAt() {
        return new Date(this.#createdAt);
    }

    /**
     * Get last update date (read-only)
     * @returns {Date} Last update date
     */
    get updatedAt() {
        return new Date(this.#updatedAt);
    }

    /**
     * Get completion date (read-only)
     * @returns {Date|null} Completion date or null if not completed
     */
    get completedAt() {
        return this.#completedAt ? new Date(this.#completedAt) : null;
    }

    /**
     * Get task history (read-only copy)
     * @returns {Array} Copy of task history
     */
    get history() {
        return [...this.#history];
    }

    /**
     * Update task status
     * @param {string} newStatus - New status
     * @throws {TaskError} If status transition is invalid
     */
    updateStatus(newStatus) {
        if (!Object.values(TASK_STATUS).includes(newStatus)) {
            throw new TaskError(`Invalid status: ${newStatus}`, this.#id);
        }

        const oldStatus = this.status;
        
        // Validate status transitions
        if (oldStatus === TASK_STATUS.COMPLETED && newStatus !== TASK_STATUS.TODO) {
            throw new TaskError('Completed tasks can only be reopened to TODO status', this.#id);
        }

        if (oldStatus === TASK_STATUS.CANCELLED && newStatus === TASK_STATUS.COMPLETED) {
            throw new TaskError('Cannot complete a cancelled task', this.#id);
        }

        this.status = newStatus;
        this.#updatedAt = new Date();

        // Handle completion
        if (newStatus === TASK_STATUS.COMPLETED && oldStatus !== TASK_STATUS.COMPLETED) {
            this.#completedAt = new Date();
        } else if (newStatus !== TASK_STATUS.COMPLETED) {
            this.#completedAt = null;
        }

        this.#addToHistory('status_changed', `Status changed from ${oldStatus} to ${newStatus}`);
    }

    /**
     * Mark task as completed
     * @param {number} actualHours - Actual hours spent (optional)
     */
    complete(actualHours = null) {
        this.updateStatus(TASK_STATUS.COMPLETED);
        
        if (actualHours !== null) {
            this.setActualHours(actualHours);
        }

        this.#addToHistory('completed', 'Task marked as completed');
    }

    /**
     * Update task priority
     * @param {string} newPriority - New priority level
     * @throws {ValidationError} If priority is invalid
     */
    updatePriority(newPriority) {
        if (!Object.values(PRIORITY_LEVELS).includes(newPriority)) {
            throw new ValidationError(`Priority must be one of: ${Object.values(PRIORITY_LEVELS).join(', ')}`, 'priority');
        }

        const oldPriority = this.priority;
        this.priority = newPriority;
        this.#updatedAt = new Date();

        this.#addToHistory('priority_changed', `Priority changed from ${oldPriority} to ${newPriority}`);
    }

    /**
     * Update task details
     * @param {Object} updates - Updates to apply
     * @throws {ValidationError} If updates are invalid
     */
    updateDetails(updates) {
        if (!updates || typeof updates !== 'object') {
            throw new ValidationError('Updates must be an object', 'updates');
        }

        const changes = [];

        // Update title
        if (updates.title !== undefined) {
            if (!validateRequired(updates.title)) {
                throw new ValidationError('Title cannot be empty', 'title');
            }
            
            if (updates.title.trim().length < 3 || updates.title.trim().length > 200) {
                throw new ValidationError('Title must be between 3 and 200 characters', 'title');
            }
            
            if (updates.title.trim() !== this.title) {
                changes.push(`title: "${this.title}" → "${updates.title.trim()}"`);
                this.title = updates.title.trim();
            }
        }

        // Update description
        if (updates.description !== undefined) {
            if (updates.description && updates.description.length > 1000) {
                throw new ValidationError('Description must be less than 1000 characters', 'description');
            }
            
            const newDescription = updates.description ? updates.description.trim() : '';
            if (newDescription !== this.description) {
                changes.push('description updated');
                this.description = newDescription;
            }
        }

        // Update category
        if (updates.category !== undefined && updates.category !== this.category) {
            changes.push(`category: "${this.category}" → "${updates.category}"`);
            this.category = updates.category;
        }

        // Update due date
        if (updates.dueDate !== undefined) {
            let newDueDate = null;
            if (updates.dueDate) {
                newDueDate = new Date(updates.dueDate);
                if (isNaN(newDueDate.getTime())) {
                    throw new ValidationError('Invalid due date format', 'dueDate');
                }
            }
            
            const oldDueDateStr = this.dueDate ? this.dueDate.toDateString() : 'none';
            const newDueDateStr = newDueDate ? newDueDate.toDateString() : 'none';
            
            if (oldDueDateStr !== newDueDateStr) {
                changes.push(`due date: ${oldDueDateStr} → ${newDueDateStr}`);
                this.dueDate = newDueDate;
            }
        }

        // Update estimated hours
        if (updates.estimatedHours !== undefined) {
            if (updates.estimatedHours !== null && (typeof updates.estimatedHours !== 'number' || updates.estimatedHours < 0)) {
                throw new ValidationError('Estimated hours must be a positive number', 'estimatedHours');
            }
            
            if (updates.estimatedHours !== this.estimatedHours) {
                changes.push(`estimated hours: ${this.estimatedHours} → ${updates.estimatedHours}`);
                this.estimatedHours = updates.estimatedHours;
            }
        }

        if (changes.length > 0) {
            this.#updatedAt = new Date();
            this.#addToHistory('details_updated', changes.join(', '));
        }
    }

    /**
     * Set actual hours spent on task
     * @param {number} hours - Hours spent
     * @throws {ValidationError} If hours is invalid
     */
    setActualHours(hours) {
        if (typeof hours !== 'number' || hours < 0) {
            throw new ValidationError('Actual hours must be a positive number', 'actualHours');
        }

        this.actualHours = hours;
        this.#updatedAt = new Date();
        this.#addToHistory('actual_hours_set', `Actual hours set to ${hours}`);
    }

    /**
     * Add tags to the task
     * @param {Array<string>} newTags - Tags to add
     */
    addTags(newTags) {
        if (!Array.isArray(newTags)) {
            throw new ValidationError('Tags must be an array', 'tags');
        }

        const validTags = newTags.filter(tag => 
            typeof tag === 'string' && 
            tag.trim().length > 0 && 
            !this.tags.includes(tag.trim())
        );

        if (validTags.length > 0) {
            this.tags.push(...validTags.map(tag => tag.trim()));
            this.#updatedAt = new Date();
            this.#addToHistory('tags_added', `Added tags: ${validTags.join(', ')}`);
        }
    }

    /**
     * Remove tags from the task
     * @param {Array<string>} tagsToRemove - Tags to remove
     */
    removeTags(tagsToRemove) {
        if (!Array.isArray(tagsToRemove)) {
            throw new ValidationError('Tags to remove must be an array', 'tags');
        }

        const removedTags = [];
        tagsToRemove.forEach(tag => {
            const index = this.tags.indexOf(tag);
            if (index > -1) {
                this.tags.splice(index, 1);
                removedTags.push(tag);
            }
        });

        if (removedTags.length > 0) {
            this.#updatedAt = new Date();
            this.#addToHistory('tags_removed', `Removed tags: ${removedTags.join(', ')}`);
        }
    }

    /**
     * Check if task is overdue
     * @returns {boolean} True if task is overdue
     */
    isOverdue() {
        if (!this.dueDate || this.status === TASK_STATUS.COMPLETED) {
            return false;
        }
        
        return new Date() > this.dueDate;
    }

    /**
     * Get days until due date
     * @returns {number|null} Days until due (negative if overdue), null if no due date
     */
    getDaysUntilDue() {
        if (!this.dueDate) return null;
        
        const now = new Date();
        const diffTime = this.dueDate.getTime() - now.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    /**
     * Get task completion percentage (for progress tracking)
     * @returns {number} Completion percentage (0-100)
     */
    getCompletionPercentage() {
        switch (this.status) {
            case TASK_STATUS.TODO:
                return 0;
            case TASK_STATUS.IN_PROGRESS:
                return 50;
            case TASK_STATUS.COMPLETED:
                return 100;
            case TASK_STATUS.CANCELLED:
                return 0;
            default:
                return 0;
        }
    }

    /**
     * Get task summary information
     * @returns {Object} Task summary
     */
    getSummary() {
        return {
            id: this.#id,
            title: this.title,
            status: this.status,
            priority: this.priority,
            category: this.category,
            dueDate: this.dueDate,
            isOverdue: this.isOverdue(),
            daysUntilDue: this.getDaysUntilDue(),
            completionPercentage: this.getCompletionPercentage(),
            createdAt: this.#createdAt,
            updatedAt: this.#updatedAt,
            completedAt: this.#completedAt
        };
    }

    /**
     * Convert task to JSON-serializable object
     * @returns {Object} Serializable task data
     */
    toJSON() {
        return {
            id: this.#id,
            title: this.title,
            description: this.description,
            userId: this.userId,
            priority: this.priority,
            category: this.category,
            status: this.status,
            dueDate: this.dueDate,
            tags: [...this.tags],
            estimatedHours: this.estimatedHours,
            actualHours: this.actualHours,
            createdAt: this.#createdAt,
            updatedAt: this.#updatedAt,
            completedAt: this.#completedAt,
            history: [...this.#history]
        };
    }

    /**
     * Create Task instance from JSON data
     * @param {Object} jsonData - JSON data
     * @returns {Task} Task instance
     */
    static fromJSON(jsonData) {
        const task = new Task({
            title: jsonData.title,
            description: jsonData.description,
            userId: jsonData.userId,
            priority: jsonData.priority,
            category: jsonData.category,
            dueDate: jsonData.dueDate,
            estimatedHours: jsonData.estimatedHours
        });

        // Restore private fields
        task.#id = jsonData.id;
        task.#createdAt = new Date(jsonData.createdAt);
        task.#updatedAt = new Date(jsonData.updatedAt);
        task.#completedAt = jsonData.completedAt ? new Date(jsonData.completedAt) : null;
        task.#history = jsonData.history || [];
        
        // Restore public fields
        task.status = jsonData.status;
        task.tags = jsonData.tags || [];
        task.actualHours = jsonData.actualHours;

        return task;
    }

    /**
     * Get available priority levels
     * @returns {Object} Priority levels
     */
    static getPriorityLevels() {
        return { ...PRIORITY_LEVELS };
    }

    /**
     * Get available task statuses
     * @returns {Object} Task statuses
     */
    static getTaskStatuses() {
        return { ...TASK_STATUS };
    }
}

module.exports = { Task, PRIORITY_LEVELS, TASK_STATUS };
