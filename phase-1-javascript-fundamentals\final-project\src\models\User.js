// User Model Class
// Demonstrates: ES6 Classes, Private Fields, Validation, Modern JavaScript

const { ValidationError } = require('../utils/errors');
const { validateEmail, validateRequired } = require('../utils/validators');

/**
 * User class representing a system user
 * Demonstrates ES6 class syntax, private fields, and validation
 */
class User {
    // Private fields (ES2022 syntax)
    #id;
    #createdAt;
    #updatedAt;
    #loginHistory;

    /**
     * Create a new User instance
     * @param {Object} userData - User data object
     * @param {string} userData.name - User's full name
     * @param {string} userData.email - User's email address
     * @param {Object} userData.preferences - User preferences
     */
    constructor(userData) {
        // Validate input data
        this.#validateUserData(userData);
        
        // Initialize private fields
        this.#id = this.#generateId();
        this.#createdAt = new Date();
        this.#updatedAt = new Date();
        this.#loginHistory = [];
        
        // Public properties
        this.name = userData.name.trim();
        this.email = userData.email.toLowerCase().trim();
        this.preferences = {
            theme: 'light',
            notifications: true,
            language: 'en',
            ...userData.preferences
        };
        this.isActive = true;
        this.lastLogin = null;
    }

    /**
     * Validate user data during construction
     * @param {Object} userData - User data to validate
     * @throws {ValidationError} If validation fails
     */
    #validateUserData(userData) {
        // Check required fields
        if (!validateRequired(userData.name)) {
            throw new ValidationError('Name is required', 'name');
        }

        if (!validateRequired(userData.email)) {
            throw new ValidationError('Email is required', 'email');
        }

        // Validate name length
        if (userData.name.trim().length < 2) {
            throw new ValidationError('Name must be at least 2 characters long', 'name');
        }

        if (userData.name.trim().length > 100) {
            throw new ValidationError('Name must be less than 100 characters', 'name');
        }

        // Validate email format
        if (!validateEmail(userData.email)) {
            throw new ValidationError('Invalid email format', 'email');
        }

        // Validate preferences if provided
        if (userData.preferences && typeof userData.preferences !== 'object') {
            throw new ValidationError('Preferences must be an object', 'preferences');
        }
    }

    /**
     * Generate a unique ID for the user
     * @returns {string} Unique identifier
     */
    #generateId() {
        const timestamp = Date.now().toString(36);
        const randomPart = Math.random().toString(36).substr(2, 9);
        return `user_${timestamp}_${randomPart}`;
    }

    /**
     * Get user ID (read-only)
     * @returns {string} User ID
     */
    get id() {
        return this.#id;
    }

    /**
     * Get creation date (read-only)
     * @returns {Date} Creation date
     */
    get createdAt() {
        return new Date(this.#createdAt);
    }

    /**
     * Get last update date (read-only)
     * @returns {Date} Last update date
     */
    get updatedAt() {
        return new Date(this.#updatedAt);
    }

    /**
     * Get login history (read-only copy)
     * @returns {Array} Copy of login history
     */
    get loginHistory() {
        return [...this.#loginHistory];
    }

    /**
     * Update user preferences
     * @param {Object} newPreferences - New preference values
     * @throws {ValidationError} If preferences are invalid
     */
    updatePreferences(newPreferences) {
        if (!newPreferences || typeof newPreferences !== 'object') {
            throw new ValidationError('Preferences must be an object', 'preferences');
        }

        // Validate specific preference values
        if (newPreferences.theme && !['light', 'dark', 'auto'].includes(newPreferences.theme)) {
            throw new ValidationError('Theme must be light, dark, or auto', 'theme');
        }

        if (newPreferences.language && typeof newPreferences.language !== 'string') {
            throw new ValidationError('Language must be a string', 'language');
        }

        if (newPreferences.notifications !== undefined && typeof newPreferences.notifications !== 'boolean') {
            throw new ValidationError('Notifications must be a boolean', 'notifications');
        }

        // Update preferences
        this.preferences = {
            ...this.preferences,
            ...newPreferences
        };

        this.#updatedAt = new Date();
    }

    /**
     * Update user profile information
     * @param {Object} updates - Profile updates
     * @throws {ValidationError} If updates are invalid
     */
    updateProfile(updates) {
        if (!updates || typeof updates !== 'object') {
            throw new ValidationError('Updates must be an object', 'updates');
        }

        // Update name if provided
        if (updates.name !== undefined) {
            if (!validateRequired(updates.name)) {
                throw new ValidationError('Name cannot be empty', 'name');
            }
            
            if (updates.name.trim().length < 2 || updates.name.trim().length > 100) {
                throw new ValidationError('Name must be between 2 and 100 characters', 'name');
            }
            
            this.name = updates.name.trim();
        }

        // Update email if provided
        if (updates.email !== undefined) {
            if (!validateEmail(updates.email)) {
                throw new ValidationError('Invalid email format', 'email');
            }
            
            this.email = updates.email.toLowerCase().trim();
        }

        this.#updatedAt = new Date();
    }

    /**
     * Record a login event
     * @param {string} ipAddress - IP address of login (optional)
     * @param {string} userAgent - User agent string (optional)
     */
    recordLogin(ipAddress = 'unknown', userAgent = 'unknown') {
        const loginEvent = {
            timestamp: new Date(),
            ipAddress,
            userAgent,
            success: true
        };

        this.#loginHistory.push(loginEvent);
        this.lastLogin = new Date();
        this.#updatedAt = new Date();

        // Keep only last 10 login records
        if (this.#loginHistory.length > 10) {
            this.#loginHistory = this.#loginHistory.slice(-10);
        }
    }

    /**
     * Deactivate the user account
     */
    deactivate() {
        this.isActive = false;
        this.#updatedAt = new Date();
    }

    /**
     * Activate the user account
     */
    activate() {
        this.isActive = true;
        this.#updatedAt = new Date();
    }

    /**
     * Get user's display name
     * @returns {string} Display name
     */
    getDisplayName() {
        return this.name;
    }

    /**
     * Get user's initials
     * @returns {string} User initials
     */
    getInitials() {
        return this.name
            .split(' ')
            .map(part => part.charAt(0).toUpperCase())
            .join('')
            .substring(0, 2);
    }

    /**
     * Check if user has been active recently
     * @param {number} days - Number of days to check (default: 30)
     * @returns {boolean} True if user was active recently
     */
    isRecentlyActive(days = 30) {
        if (!this.lastLogin) return false;
        
        const daysSinceLogin = (Date.now() - this.lastLogin.getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceLogin <= days;
    }

    /**
     * Get user summary information
     * @returns {Object} User summary
     */
    getSummary() {
        return {
            id: this.#id,
            name: this.name,
            email: this.email,
            isActive: this.isActive,
            createdAt: this.#createdAt,
            lastLogin: this.lastLogin,
            loginCount: this.#loginHistory.length,
            preferences: { ...this.preferences }
        };
    }

    /**
     * Convert user to JSON-serializable object
     * @returns {Object} Serializable user data
     */
    toJSON() {
        return {
            id: this.#id,
            name: this.name,
            email: this.email,
            preferences: this.preferences,
            isActive: this.isActive,
            lastLogin: this.lastLogin,
            createdAt: this.#createdAt,
            updatedAt: this.#updatedAt,
            loginHistory: this.#loginHistory
        };
    }

    /**
     * Create User instance from JSON data
     * @param {Object} jsonData - JSON data
     * @returns {User} User instance
     */
    static fromJSON(jsonData) {
        const user = new User({
            name: jsonData.name,
            email: jsonData.email,
            preferences: jsonData.preferences
        });

        // Restore private fields
        user.#id = jsonData.id;
        user.#createdAt = new Date(jsonData.createdAt);
        user.#updatedAt = new Date(jsonData.updatedAt);
        user.#loginHistory = jsonData.loginHistory || [];
        
        // Restore public fields
        user.isActive = jsonData.isActive;
        user.lastLogin = jsonData.lastLogin ? new Date(jsonData.lastLogin) : null;

        return user;
    }

    /**
     * Validate user data for creation
     * @param {Object} userData - User data to validate
     * @returns {boolean} True if valid
     * @throws {ValidationError} If validation fails
     */
    static validate(userData) {
        if (!userData || typeof userData !== 'object') {
            throw new ValidationError('User data must be an object', 'userData');
        }

        // Use instance validation
        new User(userData);
        return true;
    }

    /**
     * Create a guest user (for demo purposes)
     * @returns {User} Guest user instance
     */
    static createGuest() {
        return new User({
            name: 'Guest User',
            email: '<EMAIL>',
            preferences: {
                theme: 'light',
                notifications: false,
                language: 'en'
            }
        });
    }
}

module.exports = User;
