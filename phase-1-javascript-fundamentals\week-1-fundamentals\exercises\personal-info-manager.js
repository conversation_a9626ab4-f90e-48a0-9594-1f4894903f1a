// Week 1 Exercise: Personal Information Manager
// Date: [Enter today's date]
// Student: [Enter your name]

console.log("=== Personal Information Manager ===");

/*
EXERCISE INSTRUCTIONS:
Create a personal information management system that demonstrates:
1. Variable declarations (var, let, const)
2. All data types (primitives and objects)
3. Type checking and conversion
4. Object and array manipulation

Complete each section below by writing the required code.
*/

// ===== SECTION 1: BASIC INFORMATION =====
console.log("\n1. Basic Information Storage:");

// TODO: Create variables for personal information
// Use const for values that won't change, let for values that might change

// Your code here:
// const firstName = "Your Name";
// let age = 25;
// etc.

console.log("First Name:", /* your variable here */);
console.log("Last Name:", /* your variable here */);
console.log("Age:", /* your variable here */);
console.log("Email:", /* your variable here */);

// ===== SECTION 2: CONTACT INFORMATION =====
console.log("\n2. Contact Information:");

// TODO: Create an object to store contact information
// Include: phone, email, address (with nested street, city, state, zip)

const contactInfo = {
    // Your code here
};

console.log("Contact Info:", contactInfo);
console.log("Phone:", /* access phone from contactInfo */);
console.log("City:", /* access city from nested address */);

// ===== SECTION 3: INTERESTS AND HOBBIES =====
console.log("\n3. Interests and Hobbies:");

// TODO: Create an array of hobbies/interests
const hobbies = [
    // Your code here - add at least 5 hobbies
];

console.log("Hobbies:", hobbies);
console.log("Number of hobbies:", /* get array length */);
console.log("First hobby:", /* get first element */);
console.log("Last hobby:", /* get last element */);

// TODO: Add a new hobby to the array
// Your code here

console.log("After adding new hobby:", hobbies);

// ===== SECTION 4: EDUCATION HISTORY =====
console.log("\n4. Education History:");

// TODO: Create an array of education objects
// Each object should have: school, degree, year, gpa
const education = [
    {
        // Your code here - add at least 2 education entries
    }
];

console.log("Education:", education);

// TODO: Access and display information from the education array
console.log("Latest school:", /* access the most recent school */);
console.log("Highest GPA:", /* find the highest GPA - you can do this manually for now */);

// ===== SECTION 5: SKILLS AND PROFICIENCY =====
console.log("\n5. Skills and Proficiency:");

// TODO: Create an object where keys are skills and values are proficiency levels (1-10)
const skills = {
    // Your code here - add at least 5 skills with proficiency levels
    // Example: "JavaScript": 7,
};

console.log("Skills:", skills);

// TODO: Add a new skill
// Your code here

// TODO: Update an existing skill level
// Your code here

console.log("Updated skills:", skills);

// ===== SECTION 6: TYPE CHECKING AND VALIDATION =====
console.log("\n6. Type Checking and Validation:");

// TODO: Create a function to validate personal information
function validatePersonalInfo(info) {
    // Your code here
    // Check if required fields exist and are the correct type
    // Return true if valid, false if invalid
    
    // Example checks:
    // - firstName should be a string and not empty
    // - age should be a number and positive
    // - email should be a string and contain '@'
    
    return true; // Replace with your validation logic
}

// TODO: Test your validation function
const testInfo = {
    firstName: "John",
    age: 25,
    email: "<EMAIL>"
};

console.log("Is test info valid?", validatePersonalInfo(testInfo));

// ===== SECTION 7: DATA CONVERSION =====
console.log("\n7. Data Conversion:");

// TODO: Practice type conversion
const stringAge = "25";
const stringGPA = "3.75";
const booleanString = "true";

// Convert and display:
console.log("String age to number:", /* convert stringAge to number */);
console.log("String GPA to number:", /* convert stringGPA to number */);
console.log("Boolean string to boolean:", /* convert booleanString to boolean */);

// TODO: Convert numbers to strings
const numericAge = 30;
const numericGPA = 3.85;

console.log("Numeric age to string:", /* convert numericAge to string */);
console.log("Numeric GPA to string:", /* convert numericGPA to string */);

// ===== SECTION 8: COMPLETE PROFILE =====
console.log("\n8. Complete Profile:");

// TODO: Create a comprehensive profile object that includes all the information above
const completeProfile = {
    // Your code here - combine all the information into one object
    // Include: personal info, contact info, hobbies, education, skills
};

console.log("Complete Profile:", completeProfile);

// ===== SECTION 9: PROFILE SUMMARY =====
console.log("\n9. Profile Summary:");

// TODO: Create a function that generates a summary string of the profile
function generateProfileSummary(profile) {
    // Your code here
    // Return a formatted string that summarizes the profile
    // Example: "John Doe, age 25, lives in New York. Has 5 hobbies and 3 skills."
    
    return "Profile summary goes here"; // Replace with your implementation
}

console.log("Profile Summary:", generateProfileSummary(completeProfile));

// ===== BONUS CHALLENGES =====
console.log("\n=== Bonus Challenges ===");

// BONUS 1: Create a function to find the average GPA from education array
function calculateAverageGPA(educationArray) {
    // Your code here
    return 0; // Replace with your calculation
}

console.log("Average GPA:", calculateAverageGPA(education));

// BONUS 2: Create a function to get skills above a certain proficiency level
function getSkillsAboveLevel(skillsObject, minLevel) {
    // Your code here
    // Return an array of skill names that have proficiency >= minLevel
    return []; // Replace with your implementation
}

console.log("Skills above level 7:", getSkillsAboveLevel(skills, 7));

// BONUS 3: Create a function to format contact information for display
function formatContactInfo(contact) {
    // Your code here
    // Return a nicely formatted string with contact information
    return "Formatted contact info"; // Replace with your implementation
}

console.log("Formatted Contact:", formatContactInfo(contactInfo));

console.log("\n=== Personal Information Manager Complete ===");

/*
SELF-CHECK QUESTIONS:
1. Did you use const for values that don't change?
2. Did you use let for values that might change?
3. Did you create objects with nested properties?
4. Did you create and manipulate arrays?
5. Did you practice type checking with typeof?
6. Did you practice type conversion?
7. Can you explain what each part of your code does?

NEXT STEPS:
1. Test your code by running it: node personal-info-manager.js
2. Try modifying values and see what happens
3. Add more features like updating information
4. Share your solution with others for feedback
*/
