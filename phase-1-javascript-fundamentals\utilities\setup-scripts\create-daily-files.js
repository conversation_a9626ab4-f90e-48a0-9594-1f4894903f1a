const fs = require('fs');
const path = require('path');

/**
 * Creates daily learning files for a specific week and day
 * Usage: node create-daily-files.js <week> <day> <topic>
 * Example: node create-daily-files.js 1 1 variables-types
 */

function createDailyFiles(week, day, topic) {
    const weekFolders = {
        1: 'week-1-fundamentals',
        2: 'week-2-functions-scope',
        3: 'week-3-asynchronous-js',
        4: 'week-4-modern-js-errors'
    };
    
    const weekFolder = weekFolders[week];
    if (!weekFolder) {
        console.error('Invalid week number. Use 1, 2, 3, or 4.');
        return;
    }
    
    const basePath = path.join(__dirname, '..', '..', weekFolder);
    
    // Check if week folder exists
    if (!fs.existsSync(basePath)) {
        console.error(`Week folder ${weekFolder} does not exist.`);
        return;
    }
    
    const notesPath = path.join(basePath, 'notes');
    const examplesPath = path.join(basePath, 'code-examples');
    
    // Ensure directories exist
    if (!fs.existsSync(notesPath)) {
        fs.mkdirSync(notesPath, { recursive: true });
    }
    if (!fs.existsSync(examplesPath)) {
        fs.mkdirSync(examplesPath, { recursive: true });
    }
    
    const notesFile = path.join(notesPath, `day-${day}-${topic}.md`);
    const exampleFile = path.join(examplesPath, `${topic}-examples.js`);
    
    // Create note file with template
    const noteTemplate = `# Day ${day}: ${topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}

## Learning Objectives
By the end of this session, you will:
- [ ] Understand the core concepts of ${topic.replace(/-/g, ' ')}
- [ ] Be able to implement practical examples
- [ ] Complete hands-on exercises
- [ ] Apply concepts in real-world scenarios

## Key Concepts

### Concept 1: [Main Topic]
[Take notes on the main concept here]

**Important Points:**
- Point 1
- Point 2
- Point 3

### Concept 2: [Secondary Topic]
[Take notes on secondary concepts here]

**Code Example:**
\`\`\`javascript
// Add relevant code examples here
\`\`\`

### Concept 3: [Advanced Topic]
[Take notes on advanced concepts here]

## Code Examples
See: ../code-examples/${topic}-examples.js

## Practice Exercises
- [ ] Exercise 1: [Description]
- [ ] Exercise 2: [Description]
- [ ] Exercise 3: [Description]

## Common Mistakes to Avoid
1. Mistake 1: [Description]
   - Solution: [How to avoid it]

2. Mistake 2: [Description]
   - Solution: [How to avoid it]

## Questions & Clarifications
- Question 1: [Your question]
  - Answer: [Research and write the answer]

- Question 2: [Your question]
  - Answer: [Research and write the answer]

## Real-World Applications
- Application 1: [How this concept is used in real projects]
- Application 2: [Another practical use case]

## Summary
[Write a brief summary of what you learned today]

## Tomorrow's Preparation
- [ ] Review today's concepts
- [ ] Complete any unfinished exercises
- [ ] Read introduction to [next topic]
- [ ] Prepare questions for tomorrow's session

## Additional Resources
- [Link to helpful documentation]
- [Link to tutorial or video]
- [Link to practice problems]

---
**Date:** ${new Date().toDateString()}
**Time Spent:** [Record your study time]
**Difficulty Level:** [1-5 scale]
**Confidence Level:** [1-5 scale]
`;

    // Create example file with template
    const codeTemplate = `// Day ${day}: ${topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} Examples
// Date: ${new Date().toDateString()}
// Week: ${week}

console.log("=== ${topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} Examples ===");

// ===== BASIC EXAMPLES =====
console.log("\\n1. Basic Examples:");

// Example 1: [Description]
function example1() {
    // Your code here
    console.log("Example 1: Basic implementation");
}

// Example 2: [Description]  
function example2() {
    // Your code here
    console.log("Example 2: Intermediate implementation");
}

// Example 3: [Description]
function example3() {
    // Your code here
    console.log("Example 3: Advanced implementation");
}

// ===== PRACTICAL EXAMPLES =====
console.log("\\n2. Practical Examples:");

// Practical Example 1: Real-world scenario
function practicalExample1() {
    // Your code here
    console.log("Practical Example 1: Real-world application");
}

// Practical Example 2: Common use case
function practicalExample2() {
    // Your code here
    console.log("Practical Example 2: Common use case");
}

// ===== INTERACTIVE EXAMPLES =====
console.log("\\n3. Interactive Examples:");

// Interactive Example: User input simulation
function interactiveExample() {
    // Your code here
    console.log("Interactive Example: User interaction simulation");
}

// ===== TEST YOUR EXAMPLES =====
console.log("\\n4. Testing Examples:");

// Test all examples
function runAllExamples() {
    console.log("Running all examples...");
    
    example1();
    example2();
    example3();
    practicalExample1();
    practicalExample2();
    interactiveExample();
    
    console.log("All examples completed!");
}

// Run the examples
runAllExamples();

console.log("\\n=== Examples Complete ===");

// ===== PRACTICE EXERCISES =====
console.log("\\n=== Practice Exercises ===");
console.log("Try these exercises to reinforce your learning:");
console.log("1. Modify example1() to add your own functionality");
console.log("2. Create a variation of example2() with different parameters");
console.log("3. Combine concepts from multiple examples");
console.log("4. Build something new using today's concepts");

// ===== DEBUGGING PRACTICE =====
console.log("\\n=== Debugging Practice ===");
console.log("Try to find and fix bugs in these examples:");

// Buggy Example 1 (intentionally contains errors for practice)
function buggyExample1() {
    // Add some intentional bugs here for practice
    console.log("Buggy Example 1: Find and fix the bugs!");
}

// Buggy Example 2 (intentionally contains errors for practice)
function buggyExample2() {
    // Add some intentional bugs here for practice
    console.log("Buggy Example 2: More debugging practice!");
}

console.log("\\n=== End of Examples ===");
`;

    try {
        // Write the files
        fs.writeFileSync(notesFile, noteTemplate);
        fs.writeFileSync(exampleFile, codeTemplate);
        
        console.log(`✅ Created files for Week ${week}, Day ${day}: ${topic}`);
        console.log(`📝 Notes file: ${notesFile}`);
        console.log(`💻 Code file: ${exampleFile}`);
        console.log(`\\n🚀 Ready to start learning! Open the files and begin your study session.`);
        
    } catch (error) {
        console.error('Error creating files:', error.message);
    }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length !== 3) {
    console.log('Usage: node create-daily-files.js <week> <day> <topic>');
    console.log('');
    console.log('Examples:');
    console.log('  node create-daily-files.js 1 1 variables-types');
    console.log('  node create-daily-files.js 1 2 operators-control');
    console.log('  node create-daily-files.js 2 1 function-basics');
    console.log('  node create-daily-files.js 3 1 callbacks-promises');
    console.log('');
    console.log('Week options: 1, 2, 3, 4');
    console.log('Day options: 1, 2, 3, 4, 5');
    console.log('Topic: Use kebab-case (lowercase with hyphens)');
    process.exit(1);
}

const [week, day, topic] = args;

// Validate inputs
if (!['1', '2', '3', '4'].includes(week)) {
    console.error('Week must be 1, 2, 3, or 4');
    process.exit(1);
}

if (!['1', '2', '3', '4', '5'].includes(day)) {
    console.error('Day must be 1, 2, 3, 4, or 5');
    process.exit(1);
}

if (!topic || topic.length === 0) {
    console.error('Topic cannot be empty');
    process.exit(1);
}

// Create the files
createDailyFiles(parseInt(week), parseInt(day), topic);
