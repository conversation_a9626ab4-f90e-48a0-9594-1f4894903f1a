const fs = require('fs');
const path = require('path');

/**
 * Progress Tracker for Phase 1 JavaScript Fundamentals
 * Analyzes progress files and generates summary reports
 * Usage: node progress-tracker.js
 */

class ProgressTracker {
    constructor() {
        this.basePath = path.join(__dirname, '..', '..');
        this.weeks = [
            { folder: 'week-1-fundamentals', name: 'Variables, Types, Control Structures' },
            { folder: 'week-2-functions-scope', name: 'Functions, Scope, Closures' },
            { folder: 'week-3-asynchronous-js', name: 'Asynchronous JavaScript' },
            { folder: 'week-4-modern-js-errors', name: 'Modern JS Features & Error Handling' }
        ];
    }

    /**
     * Calculate progress for all weeks
     */
    calculateProgress() {
        console.log('🔍 Analyzing Phase 1 Progress...\n');
        
        let totalProgress = 0;
        let weeklyProgress = [];
        let detailedResults = [];
        
        this.weeks.forEach((week, index) => {
            const weekNumber = index + 1;
            const progressFile = path.join(this.basePath, week.folder, `WEEK-${weekNumber}-PROGRESS.md`);
            
            let weekProgress = 0;
            let weekDetails = {
                week: weekNumber,
                name: week.name,
                folder: week.folder,
                progress: 0,
                completedTasks: 0,
                totalTasks: 0,
                status: 'Not Started',
                files: {
                    progressFile: fs.existsSync(progressFile),
                    activitiesFile: fs.existsSync(path.join(this.basePath, week.folder, `WEEK-${weekNumber}-ACTIVITIES.md`)),
                    notesCount: this.countFiles(path.join(this.basePath, week.folder, 'notes'), '.md'),
                    examplesCount: this.countFiles(path.join(this.basePath, week.folder, 'code-examples'), '.js'),
                    exercisesCount: this.countFiles(path.join(this.basePath, week.folder, 'exercises'), '.js')
                }
            };
            
            if (fs.existsSync(progressFile)) {
                const content = fs.readFileSync(progressFile, 'utf8');
                
                // Count completed tasks (✅)
                const completedTasks = (content.match(/✅/g) || []).length;
                const inProgressTasks = (content.match(/🔄/g) || []).length;
                const totalCheckboxes = (content.match(/- \[.\]/g) || []).length;
                
                weekProgress = totalCheckboxes > 0 ? (completedTasks / totalCheckboxes) * 100 : 0;
                
                weekDetails.completedTasks = completedTasks;
                weekDetails.totalTasks = totalCheckboxes;
                weekDetails.inProgressTasks = inProgressTasks;
                weekDetails.progress = weekProgress;
                
                // Determine status
                if (weekProgress === 0) {
                    weekDetails.status = 'Not Started';
                } else if (weekProgress < 50) {
                    weekDetails.status = 'Getting Started';
                } else if (weekProgress < 80) {
                    weekDetails.status = 'In Progress';
                } else if (weekProgress < 100) {
                    weekDetails.status = 'Nearly Complete';
                } else {
                    weekDetails.status = 'Complete';
                }
            }
            
            weeklyProgress.push(weekProgress);
            detailedResults.push(weekDetails);
            totalProgress += weekProgress;
        });
        
        const overallProgress = totalProgress / this.weeks.length;
        
        // Display results
        this.displayProgressSummary(overallProgress, weeklyProgress, detailedResults);
        
        // Update main progress file
        this.updateMainProgressFile(overallProgress, weeklyProgress, detailedResults);
        
        // Generate recommendations
        this.generateRecommendations(detailedResults);
        
        return {
            overallProgress,
            weeklyProgress,
            detailedResults
        };
    }

    /**
     * Count files in a directory with specific extension
     */
    countFiles(dirPath, extension) {
        if (!fs.existsSync(dirPath)) return 0;
        
        try {
            const files = fs.readdirSync(dirPath);
            return files.filter(file => file.endsWith(extension)).length;
        } catch (error) {
            return 0;
        }
    }

    /**
     * Display progress summary in console
     */
    displayProgressSummary(overallProgress, weeklyProgress, detailedResults) {
        console.log('📊 PHASE 1 PROGRESS SUMMARY');
        console.log('=' .repeat(50));
        console.log(`🎯 Overall Progress: ${overallProgress.toFixed(1)}%`);
        console.log(`📅 Last Updated: ${new Date().toDateString()}\n`);
        
        console.log('📋 WEEKLY BREAKDOWN:');
        detailedResults.forEach((week, index) => {
            const progressBar = this.createProgressBar(week.progress);
            const statusEmoji = this.getStatusEmoji(week.status);
            
            console.log(`Week ${week.week}: ${week.name}`);
            console.log(`   ${progressBar} ${week.progress.toFixed(1)}% ${statusEmoji} ${week.status}`);
            console.log(`   Tasks: ${week.completedTasks}/${week.totalTasks} completed`);
            console.log(`   Files: ${week.files.notesCount} notes, ${week.files.examplesCount} examples, ${week.files.exercisesCount} exercises\n`);
        });
        
        console.log('📈 PROGRESS CHART:');
        weeklyProgress.forEach((progress, index) => {
            const week = detailedResults[index];
            const bar = '█'.repeat(Math.floor(progress / 5)) + '░'.repeat(20 - Math.floor(progress / 5));
            console.log(`Week ${index + 1}: [${bar}] ${progress.toFixed(1)}%`);
        });
    }

    /**
     * Create a visual progress bar
     */
    createProgressBar(percentage, length = 20) {
        const filled = Math.floor((percentage / 100) * length);
        const empty = length - filled;
        return '[' + '█'.repeat(filled) + '░'.repeat(empty) + ']';
    }

    /**
     * Get status emoji
     */
    getStatusEmoji(status) {
        const emojis = {
            'Not Started': '⏳',
            'Getting Started': '🚀',
            'In Progress': '🔄',
            'Nearly Complete': '🎯',
            'Complete': '✅'
        };
        return emojis[status] || '❓';
    }

    /**
     * Update the main PROGRESS.md file
     */
    updateMainProgressFile(overallProgress, weeklyProgress, detailedResults) {
        const progressContent = `# Phase 1 Progress Update
Generated: ${new Date().toDateString()}

## Overall Progress: ${overallProgress.toFixed(1)}%

${this.createProgressBar(overallProgress, 30)} ${overallProgress.toFixed(1)}%

## Weekly Progress

${detailedResults.map((week, index) => 
    `- Week ${week.week}: ${week.name} - ${weeklyProgress[index].toFixed(1)}% ${this.getStatusEmoji(week.status)}`
).join('\n')}

## Detailed Breakdown

${detailedResults.map(week => `
### Week ${week.week}: ${week.name}
- **Progress**: ${week.progress.toFixed(1)}%
- **Status**: ${week.status} ${this.getStatusEmoji(week.status)}
- **Tasks**: ${week.completedTasks}/${week.totalTasks} completed
- **Files Created**: ${week.files.notesCount} notes, ${week.files.examplesCount} examples, ${week.files.exercisesCount} exercises
`).join('')}

## Next Actions

${detailedResults.map((week, index) => {
    if (weeklyProgress[index] < 100) {
        return `- Complete remaining tasks in Week ${week.week}: ${week.name}`;
    }
    return null;
}).filter(Boolean).join('\n')}

## Study Statistics

- **Total Study Sessions**: ${detailedResults.reduce((sum, week) => sum + week.completedTasks, 0)}
- **Files Created**: ${detailedResults.reduce((sum, week) => sum + week.files.notesCount + week.files.examplesCount + week.files.exercisesCount, 0)}
- **Weeks Completed**: ${detailedResults.filter(week => week.progress === 100).length}/4

## Phase 1 Readiness

${overallProgress >= 80 ? '✅ Ready to move to Phase 2: Node.js Fundamentals!' : '⏳ Continue working on Phase 1 fundamentals'}

---
*Last updated: ${new Date().toISOString()}*
`;
        
        const progressFilePath = path.join(this.basePath, 'PROGRESS.md');
        fs.writeFileSync(progressFilePath, progressContent);
        console.log(`\n💾 Progress updated in PROGRESS.md`);
    }

    /**
     * Generate personalized recommendations
     */
    generateRecommendations(detailedResults) {
        console.log('\n💡 RECOMMENDATIONS:');
        console.log('=' .repeat(50));
        
        const incompleteWeeks = detailedResults.filter(week => week.progress < 100);
        const currentWeek = detailedResults.find(week => week.progress > 0 && week.progress < 100);
        
        if (incompleteWeeks.length === 0) {
            console.log('🎉 Congratulations! You\'ve completed all Phase 1 activities!');
            console.log('🚀 You\'re ready to move on to Phase 2: Node.js Fundamentals');
            return;
        }
        
        if (currentWeek) {
            console.log(`📚 Focus on Week ${currentWeek.week}: ${currentWeek.name}`);
            console.log(`   You're ${currentWeek.progress.toFixed(1)}% complete with this week.`);
            
            if (currentWeek.files.notesCount === 0) {
                console.log('   📝 Start by taking notes on the key concepts');
            }
            if (currentWeek.files.examplesCount === 0) {
                console.log('   💻 Work through the code examples');
            }
            if (currentWeek.files.exercisesCount === 0) {
                console.log('   🏋️ Complete the practice exercises');
            }
        } else {
            const nextWeek = incompleteWeeks[0];
            console.log(`🎯 Start Week ${nextWeek.week}: ${nextWeek.name}`);
            console.log('   📖 Begin with reading the weekly activities guide');
            console.log('   📝 Take detailed notes as you learn');
        }
        
        // General recommendations
        console.log('\n🎯 General Tips:');
        console.log('   • Spend 2-3 hours daily on consistent learning');
        console.log('   • Practice coding every concept you learn');
        console.log('   • Take breaks when you feel overwhelmed');
        console.log('   • Review previous concepts regularly');
        console.log('   • Ask questions and seek help when stuck');
    }
}

// Main execution
if (require.main === module) {
    const tracker = new ProgressTracker();
    tracker.calculateProgress();
}
