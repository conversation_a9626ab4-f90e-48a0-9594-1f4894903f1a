# Week 1 Assessment: Variables, Data Types & Control Structures

## Instructions
Complete this assessment to evaluate your understanding of Week 1 concepts. Answer all questions and complete all coding exercises. This assessment should take 60-90 minutes.

**Assessment Date:** [Enter date]
**Student Name:** [Enter your name]

---

## Part 1: Multiple Choice Questions (20 points)

### Question 1 (2 points)
Which of the following variable declarations can be reassigned?
a) `const name = "<PERSON>";`
b) `let age = 25;`
c) `var city = "New York";`
d) Both b and c

**Your Answer:** ___

### Question 2 (2 points)
What is the output of `typeof null`?
a) "null"
b) "undefined"
c) "object"
d) "boolean"

**Your Answer:** ___

### Question 3 (2 points)
Which operator checks for both value and type equality?
a) `==`
b) `===`
c) `!=`
d) `!==`

**Your Answer:** ___

### Question 4 (2 points)
What will `Boolean("")` return?
a) true
b) false
c) undefined
d) null

**Your Answer:** ___

### Question 5 (2 points)
Which loop is guaranteed to execute at least once?
a) for loop
b) while loop
c) do-while loop
d) for-in loop

**Your Answer:** ___

### Question 6 (2 points)
What is the correct way to access a property called "firstName" in an object named "person"?
a) `person->firstName`
b) `person.firstName`
c) `person[firstName]`
d) `person::firstName`

**Your Answer:** ___

### Question 7 (2 points)
Which of the following is NOT a primitive data type in JavaScript?
a) string
b) number
c) array
d) boolean

**Your Answer:** ___

### Question 8 (2 points)
What does the `%` operator do?
a) Percentage calculation
b) Modulus (remainder)
c) Division
d) Multiplication

**Your Answer:** ___

### Question 9 (2 points)
Which statement about `var` is true?
a) It is block-scoped
b) It cannot be redeclared
c) It is function-scoped
d) It cannot be reassigned

**Your Answer:** ___

### Question 10 (2 points)
What will `5 + "3"` equal in JavaScript?
a) 8
b) "53"
c) 53
d) Error

**Your Answer:** ___

---

## Part 2: Short Answer Questions (20 points)

### Question 11 (4 points)
Explain the difference between `let`, `const`, and `var` in terms of scope and reassignment.

**Your Answer:**
```
[Write your explanation here]
```

### Question 12 (4 points)
List all the falsy values in JavaScript and explain why they are considered falsy.

**Your Answer:**
```
[Write your answer here]
```

### Question 13 (4 points)
Explain the difference between `==` and `===` operators with examples.

**Your Answer:**
```
[Write your answer here]
```

### Question 14 (4 points)
What is type coercion? Give two examples of implicit type coercion in JavaScript.

**Your Answer:**
```
[Write your answer here]
```

### Question 15 (4 points)
Explain when you would use a `for` loop vs a `while` loop vs a `do-while` loop.

**Your Answer:**
```
[Write your answer here]
```

---

## Part 3: Code Reading and Analysis (20 points)

### Question 16 (5 points)
What will the following code output? Explain your reasoning.

```javascript
let x = 5;
let y = "5";
console.log(x == y);
console.log(x === y);
console.log(typeof x);
console.log(typeof y);
```

**Your Answer:**
```
Output:
[Write the output here]

Explanation:
[Explain why this output occurs]
```

### Question 17 (5 points)
Identify and fix the errors in this code:

```javascript
const name = "John";
name = "Jane";

let age;
console.log(age + 5);

var city = "New York"
var city = "Boston";
console.log(city);
```

**Your Answer:**
```javascript
// Fixed code:
[Write the corrected code here]

// Explanation of errors:
[Explain what was wrong and why your fixes work]
```

### Question 18 (5 points)
What will this code output and why?

```javascript
for (let i = 0; i < 3; i++) {
    if (i === 1) {
        continue;
    }
    console.log(i);
}
```

**Your Answer:**
```
Output:
[Write the output here]

Explanation:
[Explain the flow of execution]
```

### Question 19 (5 points)
Analyze this object and answer the questions:

```javascript
const student = {
    name: "Alice",
    grades: [85, 92, 78],
    isActive: true,
    address: {
        street: "123 Main St",
        city: "Boston"
    }
};
```

a) How would you access Alice's city?
b) How would you add a new grade of 95?
c) How would you change the student's name to "Bob"?

**Your Answer:**
```javascript
a) [Write the code to access city]
b) [Write the code to add grade]
c) [Write the code to change name]
```

---

## Part 4: Coding Exercises (40 points)

### Exercise 1: Variable Practice (10 points)
Create a program that demonstrates the use of all three variable declaration types (`var`, `let`, `const`) and all primitive data types.

```javascript
// Your code here:
```

### Exercise 2: Type Conversion (10 points)
Write a function called `convertAndValidate` that:
- Takes a string parameter
- Tries to convert it to a number
- Returns an object with the original value, converted value, and whether the conversion was successful

```javascript
// Your code here:
function convertAndValidate(input) {
    // Your implementation
}

// Test cases:
console.log(convertAndValidate("123"));    // Should work
console.log(convertAndValidate("abc"));    // Should handle invalid input
console.log(convertAndValidate("45.67"));  // Should work with decimals
```

### Exercise 3: Control Structures (10 points)
Write a program that:
- Creates an array of numbers from 1 to 20
- Uses a loop to iterate through the array
- Prints "Fizz" for multiples of 3, "Buzz" for multiples of 5, "FizzBuzz" for multiples of both, and the number otherwise

```javascript
// Your code here:
```

### Exercise 4: Object Manipulation (10 points)
Create a `Library` object that:
- Has an array of book objects (each book has title, author, year, isAvailable)
- Has methods to add a book, find a book by title, and list all available books
- Demonstrates proper use of object properties and methods

```javascript
// Your code here:
const library = {
    // Your implementation
};

// Test your implementation:
// Add some books and test the methods
```

---

## Part 5: Problem Solving (Bonus - 10 points)

### Bonus Challenge
Create a simple calculator that:
- Takes two numbers and an operator as input
- Supports +, -, *, /, and % operations
- Handles division by zero
- Returns both the result and a description of the operation

```javascript
// Your code here:
function calculator(num1, operator, num2) {
    // Your implementation
}

// Test cases:
console.log(calculator(10, "+", 5));   // Should return result and description
console.log(calculator(10, "/", 0));   // Should handle division by zero
console.log(calculator(10, "%", 3));   // Should handle modulus
```

---

## Self-Evaluation

### Confidence Level (1-5 scale)
Rate your confidence in each area:

- Variable declarations: ___/5
- Data types: ___/5
- Operators: ___/5
- Control structures: ___/5
- Objects and arrays: ___/5
- Type conversion: ___/5

### Time Spent
- Total time on assessment: ___ minutes
- Most challenging part: _______________
- Easiest part: _______________

### Reflection
What concepts do you need to review more?
```
[Write your reflection here]
```

What are you most proud of in this assessment?
```
[Write your reflection here]
```

---

## Answer Key (For Self-Checking)

### Part 1 Answers:
1. d) Both b and c
2. c) "object"
3. b) `===`
4. b) false
5. c) do-while loop
6. b) `person.firstName`
7. c) array
8. b) Modulus (remainder)
9. c) It is function-scoped
10. b) "53"

### Scoring Guide:
- **90-100 points**: Excellent understanding - Ready for Week 2
- **80-89 points**: Good understanding - Review weak areas
- **70-79 points**: Satisfactory - Need more practice
- **Below 70 points**: Need to review Week 1 materials

---

**Assessment Complete!** 
Review your answers and identify areas for improvement before moving to Week 2.
