// Final Project: Personal Task Management System
// Main Application Entry Point
// Author: [Your Name]
// Date: [Current Date]

// Import required modules
const UserService = require('./services/UserService');
const TaskService = require('./services/TaskService');
const FileService = require('./services/FileService');
const { ValidationError, TaskError } = require('./utils/errors');

/**
 * Main Application Class
 * Orchestrates the entire task management system
 */
class TaskManagementApp {
    constructor() {
        this.userService = new UserService();
        this.taskService = new TaskService();
        this.fileService = new FileService();
        this.currentUser = null;
        this.isRunning = false;
    }

    /**
     * Initialize the application
     * Load data and set up services
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Task Management System...');
            
            // Load existing data
            await this.loadApplicationData();
            
            // Display welcome message
            this.displayWelcomeMessage();
            
            console.log('✅ Application initialized successfully!');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize application:', error.message);
            return false;
        }
    }

    /**
     * Load application data from files
     */
    async loadApplicationData() {
        try {
            // Load users
            const userData = await this.fileService.loadUsers();
            if (userData && userData.length > 0) {
                this.userService.loadUsers(userData);
                console.log(`📊 Loaded ${userData.length} users`);
            }

            // Load tasks
            const taskData = await this.fileService.loadTasks();
            if (taskData && taskData.length > 0) {
                this.taskService.loadTasks(taskData);
                console.log(`📋 Loaded ${taskData.length} tasks`);
            }
        } catch (error) {
            console.warn('⚠️ Could not load existing data:', error.message);
            console.log('Starting with fresh data...');
        }
    }

    /**
     * Save application data to files
     */
    async saveApplicationData() {
        try {
            // Save users
            const users = this.userService.getAllUsers();
            await this.fileService.saveUsers(users);

            // Save tasks
            const tasks = this.taskService.getAllTasks();
            await this.fileService.saveTasks(tasks);

            console.log('💾 Data saved successfully!');
        } catch (error) {
            console.error('❌ Failed to save data:', error.message);
            throw error;
        }
    }

    /**
     * Display welcome message and system info
     */
    displayWelcomeMessage() {
        console.log('\n' + '='.repeat(50));
        console.log('🎯 PERSONAL TASK MANAGEMENT SYSTEM');
        console.log('='.repeat(50));
        console.log('📚 Phase 1 Final Project - JavaScript Fundamentals');
        console.log('🚀 Demonstrating: Variables, Functions, Async, Modern JS');
        console.log('='.repeat(50) + '\n');
    }

    /**
     * Start the main application loop
     */
    async start() {
        const initialized = await this.initialize();
        if (!initialized) {
            console.log('❌ Application failed to start');
            return;
        }

        this.isRunning = true;
        
        try {
            // Demo mode - showcase all features
            await this.runDemoMode();
            
            // Interactive mode would go here in a real application
            // await this.runInteractiveMode();
            
        } catch (error) {
            console.error('❌ Application error:', error.message);
        } finally {
            await this.shutdown();
        }
    }

    /**
     * Run demonstration mode to showcase all features
     */
    async runDemoMode() {
        console.log('🎬 Running Demo Mode - Showcasing All Features\n');

        try {
            // 1. User Management Demo
            await this.demoUserManagement();
            
            // 2. Task Management Demo
            await this.demoTaskManagement();
            
            // 3. Advanced Features Demo
            await this.demoAdvancedFeatures();
            
            // 4. Error Handling Demo
            await this.demoErrorHandling();
            
            console.log('🎉 Demo completed successfully!');
            
        } catch (error) {
            console.error('❌ Demo failed:', error.message);
        }
    }

    /**
     * Demonstrate user management features
     */
    async demoUserManagement() {
        console.log('👤 === USER MANAGEMENT DEMO ===');
        
        try {
            // Create sample users
            const user1 = await this.userService.createUser({
                name: 'John Doe',
                email: '<EMAIL>',
                preferences: {
                    theme: 'dark',
                    notifications: true
                }
            });
            
            const user2 = await this.userService.createUser({
                name: 'Jane Smith',
                email: '<EMAIL>',
                preferences: {
                    theme: 'light',
                    notifications: false
                }
            });

            console.log('✅ Created users:', user1.name, 'and', user2.name);
            
            // Simulate login
            this.currentUser = await this.userService.authenticateUser('<EMAIL>');
            console.log('🔐 Logged in as:', this.currentUser.name);
            
            // Update user preferences
            await this.userService.updateUserPreferences(this.currentUser.id, {
                theme: 'auto',
                language: 'en'
            });
            console.log('⚙️ Updated user preferences');
            
        } catch (error) {
            console.error('❌ User management demo failed:', error.message);
        }
        
        console.log(''); // Empty line for spacing
    }

    /**
     * Demonstrate task management features
     */
    async demoTaskManagement() {
        console.log('📋 === TASK MANAGEMENT DEMO ===');
        
        if (!this.currentUser) {
            console.log('❌ No user logged in for task demo');
            return;
        }

        try {
            // Create sample tasks
            const tasks = [
                {
                    title: 'Complete Phase 1 Final Project',
                    description: 'Build a comprehensive task management system',
                    priority: 'high',
                    category: 'Education',
                    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
                },
                {
                    title: 'Review JavaScript Fundamentals',
                    description: 'Go through all Phase 1 concepts',
                    priority: 'medium',
                    category: 'Education',
                    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
                },
                {
                    title: 'Plan Phase 2 Learning',
                    description: 'Research Node.js and backend development',
                    priority: 'low',
                    category: 'Planning',
                    dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // 14 days from now
                }
            ];

            // Create tasks
            const createdTasks = [];
            for (const taskData of tasks) {
                const task = await this.taskService.createTask(this.currentUser.id, taskData);
                createdTasks.push(task);
                console.log(`✅ Created task: "${task.title}"`);
            }

            // Update a task
            const firstTask = createdTasks[0];
            await this.taskService.updateTaskStatus(firstTask.id, 'in-progress');
            console.log(`🔄 Updated task status: "${firstTask.title}" -> in-progress`);

            // Complete a task
            const secondTask = createdTasks[1];
            await this.taskService.completeTask(secondTask.id);
            console.log(`✅ Completed task: "${secondTask.title}"`);

            // Display task statistics
            const stats = await this.taskService.getTaskStatistics(this.currentUser.id);
            console.log('📊 Task Statistics:', stats);

        } catch (error) {
            console.error('❌ Task management demo failed:', error.message);
        }
        
        console.log(''); // Empty line for spacing
    }

    /**
     * Demonstrate advanced features
     */
    async demoAdvancedFeatures() {
        console.log('🚀 === ADVANCED FEATURES DEMO ===');
        
        if (!this.currentUser) {
            console.log('❌ No user logged in for advanced demo');
            return;
        }

        try {
            // Search tasks
            const searchResults = await this.taskService.searchTasks(this.currentUser.id, 'Phase');
            console.log(`🔍 Search results for "Phase": ${searchResults.length} tasks found`);

            // Filter tasks by priority
            const highPriorityTasks = await this.taskService.getTasksByPriority(this.currentUser.id, 'high');
            console.log(`⚡ High priority tasks: ${highPriorityTasks.length} found`);

            // Get tasks by category
            const educationTasks = await this.taskService.getTasksByCategory(this.currentUser.id, 'Education');
            console.log(`📚 Education tasks: ${educationTasks.length} found`);

            // Generate report
            const report = await this.taskService.generateProductivityReport(this.currentUser.id);
            console.log('📈 Productivity Report Generated:', {
                totalTasks: report.totalTasks,
                completionRate: `${report.completionRate}%`,
                averageCompletionTime: `${report.averageCompletionTime} days`
            });

        } catch (error) {
            console.error('❌ Advanced features demo failed:', error.message);
        }
        
        console.log(''); // Empty line for spacing
    }

    /**
     * Demonstrate error handling
     */
    async demoErrorHandling() {
        console.log('🛡️ === ERROR HANDLING DEMO ===');
        
        try {
            // Test validation errors
            console.log('Testing validation errors...');
            
            try {
                await this.userService.createUser({
                    name: '', // Invalid name
                    email: 'invalid-email' // Invalid email
                });
            } catch (error) {
                if (error instanceof ValidationError) {
                    console.log('✅ Caught validation error:', error.message);
                }
            }

            // Test task errors
            try {
                await this.taskService.getTask('non-existent-id');
            } catch (error) {
                if (error instanceof TaskError) {
                    console.log('✅ Caught task error:', error.message);
                }
            }

            // Test file operation errors
            try {
                await this.fileService.loadFromFile('non-existent-file.json');
            } catch (error) {
                console.log('✅ Caught file error:', error.message);
            }

            console.log('🛡️ Error handling working correctly!');

        } catch (error) {
            console.error('❌ Error handling demo failed:', error.message);
        }
        
        console.log(''); // Empty line for spacing
    }

    /**
     * Gracefully shutdown the application
     */
    async shutdown() {
        console.log('🔄 Shutting down application...');
        
        try {
            // Save all data before shutdown
            await this.saveApplicationData();
            
            // Create backup
            await this.fileService.createBackup();
            
            console.log('✅ Application shutdown complete');
            
        } catch (error) {
            console.error('❌ Error during shutdown:', error.message);
        }
        
        this.isRunning = false;
    }

    /**
     * Display application statistics
     */
    displayStatistics() {
        const userCount = this.userService.getUserCount();
        const taskCount = this.taskService.getTaskCount();
        
        console.log('\n📊 APPLICATION STATISTICS');
        console.log('='.repeat(30));
        console.log(`👥 Total Users: ${userCount}`);
        console.log(`📋 Total Tasks: ${taskCount}`);
        console.log(`🔐 Current User: ${this.currentUser ? this.currentUser.name : 'None'}`);
        console.log(`⚡ Status: ${this.isRunning ? 'Running' : 'Stopped'}`);
        console.log('='.repeat(30) + '\n');
    }
}

/**
 * Application entry point
 * Demonstrates modern JavaScript features and error handling
 */
async function main() {
    console.log('🎯 Starting Personal Task Management System...\n');
    
    const app = new TaskManagementApp();
    
    try {
        await app.start();
        app.displayStatistics();
        
    } catch (error) {
        console.error('💥 Fatal application error:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
    console.log('\n🛑 Received interrupt signal, shutting down gracefully...');
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Start the application
if (require.main === module) {
    main().catch(error => {
        console.error('💥 Failed to start application:', error.message);
        process.exit(1);
    });
}

module.exports = TaskManagementApp;
