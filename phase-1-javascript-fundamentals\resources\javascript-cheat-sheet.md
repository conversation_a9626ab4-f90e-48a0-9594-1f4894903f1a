# JavaScript Fundamentals Cheat Sheet

## Quick Reference for Phase 1 Concepts

---

## Variable Declarations

```javascript
// const - cannot be reassigned, block-scoped
const PI = 3.14159;

// let - can be reassigned, block-scoped
let age = 25;
age = 26; // ✅ Works

// var - can be reassigned, function-scoped (avoid in modern JS)
var name = "<PERSON>";
```

## Data Types

### Primitive Types
```javascript
// String
let text = "Hello World";
let template = `Hello ${name}`;

// Number
let integer = 42;
let float = 3.14;
let scientific = 2.5e6;

// Boolean
let isTrue = true;
let isFalse = false;

// Undefined
let undefined_var;

// Null
let empty = null;

// Symbol (ES6)
let sym = Symbol('id');

// BigInt (ES2020)
let big = 123456789012345678901234567890n;
```

### Reference Types
```javascript
// Object
let person = {
    name: "<PERSON>",
    age: 30,
    address: {
        city: "New York"
    }
};

// Array
let numbers = [1, 2, 3, 4, 5];
let mixed = [1, "hello", true, null];

// Function
function greet(name) {
    return `Hello, ${name}!`;
}
```

## Operators

### Arithmetic
```javascript
let a = 10, b = 3;
a + b;  // Addition: 13
a - b;  // Subtraction: 7
a * b;  // Multiplication: 30
a / b;  // Division: 3.333...
a % b;  // Modulus: 1
a ** b; // Exponentiation: 1000
```

### Assignment
```javascript
let x = 5;
x += 3;  // x = x + 3
x -= 2;  // x = x - 2
x *= 2;  // x = x * 2
x /= 3;  // x = x / 3
x %= 2;  // x = x % 2
```

### Comparison
```javascript
5 == "5";   // true (loose equality)
5 === "5";  // false (strict equality)
5 != "5";   // false
5 !== "5";  // true
5 > 3;      // true
5 >= 5;     // true
5 < 10;     // true
5 <= 5;     // true
```

### Logical
```javascript
true && false;  // false (AND)
true || false;  // true (OR)
!true;          // false (NOT)

// Short-circuit evaluation
let result = value || "default";
let safe = obj && obj.property;
```

### Ternary
```javascript
let status = age >= 18 ? "adult" : "minor";
```

## Control Structures

### If-Else
```javascript
if (condition) {
    // code
} else if (otherCondition) {
    // code
} else {
    // code
}
```

### Switch
```javascript
switch (value) {
    case 'option1':
        // code
        break;
    case 'option2':
        // code
        break;
    default:
        // code
}
```

### Loops
```javascript
// For loop
for (let i = 0; i < 5; i++) {
    console.log(i);
}

// While loop
let i = 0;
while (i < 5) {
    console.log(i);
    i++;
}

// Do-while loop
let j = 0;
do {
    console.log(j);
    j++;
} while (j < 5);

// For-of (arrays)
for (let item of array) {
    console.log(item);
}

// For-in (objects)
for (let key in object) {
    console.log(key, object[key]);
}
```

## Functions

### Function Declaration
```javascript
function add(a, b) {
    return a + b;
}
```

### Function Expression
```javascript
const subtract = function(a, b) {
    return a - b;
};
```

### Arrow Functions
```javascript
const multiply = (a, b) => a * b;
const divide = (a, b) => {
    if (b === 0) throw new Error("Division by zero");
    return a / b;
};
```

### Parameters
```javascript
// Default parameters
function greet(name = "World") {
    return `Hello, ${name}!`;
}

// Rest parameters
function sum(...numbers) {
    return numbers.reduce((total, num) => total + num, 0);
}

// Destructuring parameters
function displayUser({name, age, email}) {
    console.log(`${name}, ${age}, ${email}`);
}
```

## Arrays

### Common Methods
```javascript
let arr = [1, 2, 3];

// Add/Remove
arr.push(4);        // Add to end
arr.pop();          // Remove from end
arr.unshift(0);     // Add to beginning
arr.shift();        // Remove from beginning

// Transform
arr.map(x => x * 2);           // Transform each element
arr.filter(x => x > 1);        // Filter elements
arr.reduce((sum, x) => sum + x, 0); // Reduce to single value

// Search
arr.find(x => x > 2);          // Find first match
arr.findIndex(x => x > 2);     // Find index of first match
arr.includes(2);               // Check if includes value

// Iterate
arr.forEach((item, index) => {
    console.log(index, item);
});
```

## Objects

### Access Properties
```javascript
let obj = {name: "John", age: 30};

// Dot notation
obj.name;

// Bracket notation
obj["name"];
obj[variable];

// Add/modify properties
obj.email = "<EMAIL>";
obj["phone"] = "555-0123";

// Delete properties
delete obj.age;
```

### Object Methods
```javascript
let person = {
    name: "John",
    greet: function() {
        return `Hello, I'm ${this.name}`;
    },
    // ES6 method shorthand
    sayBye() {
        return `Goodbye from ${this.name}`;
    }
};
```

## ES6+ Features

### Destructuring
```javascript
// Array destructuring
const [first, second, ...rest] = [1, 2, 3, 4, 5];

// Object destructuring
const {name, age, email = "N/A"} = person;
const {name: fullName} = person; // Rename
```

### Spread Operator
```javascript
// Arrays
const arr1 = [1, 2, 3];
const arr2 = [...arr1, 4, 5]; // [1, 2, 3, 4, 5]

// Objects
const obj1 = {a: 1, b: 2};
const obj2 = {...obj1, c: 3}; // {a: 1, b: 2, c: 3}
```

### Template Literals
```javascript
const name = "John";
const age = 30;
const message = `Hello, my name is ${name} and I'm ${age} years old.`;

// Multi-line strings
const html = `
    <div>
        <h1>${name}</h1>
        <p>Age: ${age}</p>
    </div>
`;
```

## Asynchronous JavaScript

### Promises
```javascript
// Creating a Promise
const promise = new Promise((resolve, reject) => {
    if (success) {
        resolve(data);
    } else {
        reject(error);
    }
});

// Using Promises
promise
    .then(data => console.log(data))
    .catch(error => console.error(error))
    .finally(() => console.log("Done"));

// Promise methods
Promise.all([promise1, promise2]);     // Wait for all
Promise.race([promise1, promise2]);    // First to resolve
Promise.allSettled([promise1, promise2]); // Wait for all to settle
```

### Async/Await
```javascript
async function fetchData() {
    try {
        const response = await fetch('/api/data');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
}
```

## Error Handling

### Try-Catch-Finally
```javascript
try {
    // Code that might throw an error
    let result = riskyOperation();
} catch (error) {
    // Handle the error
    console.error('Error occurred:', error.message);
} finally {
    // Always runs
    cleanup();
}
```

### Custom Errors
```javascript
class ValidationError extends Error {
    constructor(message, field) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
    }
}

throw new ValidationError('Invalid email', 'email');
```

## Type Checking and Conversion

### Type Checking
```javascript
typeof "hello";        // "string"
typeof 42;             // "number"
typeof true;           // "boolean"
typeof undefined;      // "undefined"
typeof null;           // "object" (known quirk)
typeof {};             // "object"
typeof [];             // "object"
Array.isArray([]);     // true
```

### Type Conversion
```javascript
// To Number
Number("123");         // 123
parseInt("123");       // 123
parseFloat("123.45");  // 123.45
+"123";                // 123 (unary plus)

// To String
String(123);           // "123"
(123).toString();      // "123"
123 + "";              // "123"

// To Boolean
Boolean(1);            // true
Boolean(0);            // false
!!value;               // Convert to boolean
```

## Common Patterns

### Checking for Existence
```javascript
// Check if variable exists and is not null/undefined
if (variable != null) { /* ... */ }

// Check if object property exists
if ('property' in object) { /* ... */ }
if (object.hasOwnProperty('property')) { /* ... */ }

// Safe property access
const value = object && object.property && object.property.nested;
```

### Default Values
```javascript
// Using || operator
const name = user.name || "Anonymous";

// Using nullish coalescing (ES2020)
const name = user.name ?? "Anonymous";

// Function parameters
function greet(name = "World") {
    return `Hello, ${name}!`;
}
```

---

## Quick Tips

- Use `const` by default, `let` when you need to reassign
- Avoid `var` in modern JavaScript
- Use `===` instead of `==` for comparisons
- Always handle errors in async code
- Use meaningful variable and function names
- Keep functions small and focused
- Comment complex logic
- Use modern ES6+ features for cleaner code
