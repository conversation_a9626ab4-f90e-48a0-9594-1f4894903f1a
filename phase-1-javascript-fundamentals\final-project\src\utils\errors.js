// Custom Error Classes
// Demonstrates: ES6 Classes, Error Inheritance, <PERSON><PERSON><PERSON> Handling Patterns

/**
 * Base application error class
 * Extends the built-in Error class with additional functionality
 */
class AppError extends Error {
    constructor(message, code = 'APP_ERROR', statusCode = 500) {
        super(message);
        
        // Maintain proper stack trace for where our error was thrown
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
        
        this.name = this.constructor.name;
        this.code = code;
        this.statusCode = statusCode;
        this.timestamp = new Date().toISOString();
        this.isOperational = true; // Distinguishes operational errors from programming errors
    }

    /**
     * Convert error to JSON for logging/API responses
     * @returns {Object} JSON representation of error
     */
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            statusCode: this.statusCode,
            timestamp: this.timestamp,
            stack: this.stack
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        return this.message;
    }
}

/**
 * Validation error class
 * Used for input validation failures
 */
class ValidationError extends AppError {
    constructor(message, field = null, value = null) {
        super(message, 'VALIDATION_ERROR', 400);
        
        this.field = field;
        this.value = value;
        this.type = 'validation';
    }

    /**
     * Get detailed validation error information
     * @returns {Object} Validation error details
     */
    getValidationDetails() {
        return {
            field: this.field,
            value: this.value,
            message: this.message,
            code: this.code
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        if (this.field) {
            return `Validation failed for ${this.field}: ${this.message}`;
        }
        return `Validation error: ${this.message}`;
    }
}

/**
 * Task-related error class
 * Used for task operation failures
 */
class TaskError extends AppError {
    constructor(message, taskId = null, operation = null) {
        super(message, 'TASK_ERROR', 400);
        
        this.taskId = taskId;
        this.operation = operation;
        this.type = 'task';
    }

    /**
     * Get task error details
     * @returns {Object} Task error details
     */
    getTaskDetails() {
        return {
            taskId: this.taskId,
            operation: this.operation,
            message: this.message,
            code: this.code
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        if (this.taskId) {
            return `Task error (${this.taskId}): ${this.message}`;
        }
        return `Task error: ${this.message}`;
    }
}

/**
 * User-related error class
 * Used for user operation failures
 */
class UserError extends AppError {
    constructor(message, userId = null, operation = null) {
        super(message, 'USER_ERROR', 400);
        
        this.userId = userId;
        this.operation = operation;
        this.type = 'user';
    }

    /**
     * Get user error details
     * @returns {Object} User error details
     */
    getUserDetails() {
        return {
            userId: this.userId,
            operation: this.operation,
            message: this.message,
            code: this.code
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        if (this.userId) {
            return `User error (${this.userId}): ${this.message}`;
        }
        return `User error: ${this.message}`;
    }
}

/**
 * Authentication error class
 * Used for authentication and authorization failures
 */
class AuthError extends AppError {
    constructor(message, userId = null) {
        super(message, 'AUTH_ERROR', 401);
        
        this.userId = userId;
        this.type = 'authentication';
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        return 'Authentication failed. Please check your credentials.';
    }
}

/**
 * File operation error class
 * Used for file system operation failures
 */
class FileError extends AppError {
    constructor(message, filePath = null, operation = null) {
        super(message, 'FILE_ERROR', 500);
        
        this.filePath = filePath;
        this.operation = operation;
        this.type = 'file';
    }

    /**
     * Get file error details
     * @returns {Object} File error details
     */
    getFileDetails() {
        return {
            filePath: this.filePath,
            operation: this.operation,
            message: this.message,
            code: this.code
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        return 'A file operation failed. Please try again.';
    }
}

/**
 * Database operation error class
 * Used for data persistence failures
 */
class DatabaseError extends AppError {
    constructor(message, operation = null, query = null) {
        super(message, 'DATABASE_ERROR', 500);
        
        this.operation = operation;
        this.query = query;
        this.type = 'database';
    }

    /**
     * Get database error details
     * @returns {Object} Database error details
     */
    getDatabaseDetails() {
        return {
            operation: this.operation,
            query: this.query,
            message: this.message,
            code: this.code
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        return 'A data operation failed. Please try again.';
    }
}

/**
 * Network operation error class
 * Used for network-related failures
 */
class NetworkError extends AppError {
    constructor(message, url = null, statusCode = 500) {
        super(message, 'NETWORK_ERROR', statusCode);
        
        this.url = url;
        this.type = 'network';
    }

    /**
     * Get network error details
     * @returns {Object} Network error details
     */
    getNetworkDetails() {
        return {
            url: this.url,
            statusCode: this.statusCode,
            message: this.message,
            code: this.code
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        return 'A network operation failed. Please check your connection and try again.';
    }
}

/**
 * Configuration error class
 * Used for configuration-related failures
 */
class ConfigError extends AppError {
    constructor(message, configKey = null) {
        super(message, 'CONFIG_ERROR', 500);
        
        this.configKey = configKey;
        this.type = 'configuration';
    }

    /**
     * Get configuration error details
     * @returns {Object} Configuration error details
     */
    getConfigDetails() {
        return {
            configKey: this.configKey,
            message: this.message,
            code: this.code
        };
    }

    /**
     * Get user-friendly error message
     * @returns {string} User-friendly message
     */
    getUserMessage() {
        return 'A configuration error occurred. Please contact support.';
    }
}

/**
 * Error handler utility functions
 */
class ErrorHandler {
    /**
     * Check if error is operational (expected) or programming error
     * @param {Error} error - Error to check
     * @returns {boolean} True if operational error
     */
    static isOperationalError(error) {
        if (error instanceof AppError) {
            return error.isOperational;
        }
        return false;
    }

    /**
     * Log error with appropriate level
     * @param {Error} error - Error to log
     * @param {Object} context - Additional context
     */
    static logError(error, context = {}) {
        const errorInfo = {
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            context
        };

        if (error instanceof AppError) {
            errorInfo.code = error.code;
            errorInfo.type = error.type;
            errorInfo.statusCode = error.statusCode;
        }

        // In a real application, you would use a proper logging library
        console.error('Error logged:', JSON.stringify(errorInfo, null, 2));
    }

    /**
     * Handle error and return appropriate response
     * @param {Error} error - Error to handle
     * @returns {Object} Error response object
     */
    static handleError(error) {
        // Log the error
        this.logError(error);

        // Return appropriate response based on error type
        if (error instanceof AppError) {
            return {
                success: false,
                error: {
                    message: error.getUserMessage(),
                    code: error.code,
                    type: error.type,
                    statusCode: error.statusCode
                }
            };
        }

        // Handle unexpected errors
        return {
            success: false,
            error: {
                message: 'An unexpected error occurred',
                code: 'INTERNAL_ERROR',
                type: 'internal',
                statusCode: 500
            }
        };
    }

    /**
     * Create error from unknown error object
     * @param {any} error - Unknown error
     * @returns {AppError} Standardized error
     */
    static createFromUnknown(error) {
        if (error instanceof AppError) {
            return error;
        }

        if (error instanceof Error) {
            return new AppError(error.message, 'UNKNOWN_ERROR', 500);
        }

        if (typeof error === 'string') {
            return new AppError(error, 'UNKNOWN_ERROR', 500);
        }

        return new AppError('An unknown error occurred', 'UNKNOWN_ERROR', 500);
    }

    /**
     * Wrap async function with error handling
     * @param {Function} fn - Async function to wrap
     * @returns {Function} Wrapped function
     */
    static wrapAsync(fn) {
        return async (...args) => {
            try {
                return await fn(...args);
            } catch (error) {
                throw this.createFromUnknown(error);
            }
        };
    }
}

/**
 * Error factory for creating specific error types
 */
class ErrorFactory {
    /**
     * Create validation error
     * @param {string} message - Error message
     * @param {string} field - Field name
     * @param {any} value - Field value
     * @returns {ValidationError} Validation error
     */
    static validation(message, field = null, value = null) {
        return new ValidationError(message, field, value);
    }

    /**
     * Create task error
     * @param {string} message - Error message
     * @param {string} taskId - Task ID
     * @param {string} operation - Operation name
     * @returns {TaskError} Task error
     */
    static task(message, taskId = null, operation = null) {
        return new TaskError(message, taskId, operation);
    }

    /**
     * Create user error
     * @param {string} message - Error message
     * @param {string} userId - User ID
     * @param {string} operation - Operation name
     * @returns {UserError} User error
     */
    static user(message, userId = null, operation = null) {
        return new UserError(message, userId, operation);
    }

    /**
     * Create authentication error
     * @param {string} message - Error message
     * @param {string} userId - User ID
     * @returns {AuthError} Authentication error
     */
    static auth(message, userId = null) {
        return new AuthError(message, userId);
    }

    /**
     * Create file error
     * @param {string} message - Error message
     * @param {string} filePath - File path
     * @param {string} operation - Operation name
     * @returns {FileError} File error
     */
    static file(message, filePath = null, operation = null) {
        return new FileError(message, filePath, operation);
    }

    /**
     * Create database error
     * @param {string} message - Error message
     * @param {string} operation - Operation name
     * @param {string} query - Query string
     * @returns {DatabaseError} Database error
     */
    static database(message, operation = null, query = null) {
        return new DatabaseError(message, operation, query);
    }

    /**
     * Create network error
     * @param {string} message - Error message
     * @param {string} url - URL
     * @param {number} statusCode - HTTP status code
     * @returns {NetworkError} Network error
     */
    static network(message, url = null, statusCode = 500) {
        return new NetworkError(message, url, statusCode);
    }

    /**
     * Create configuration error
     * @param {string} message - Error message
     * @param {string} configKey - Configuration key
     * @returns {ConfigError} Configuration error
     */
    static config(message, configKey = null) {
        return new ConfigError(message, configKey);
    }
}

module.exports = {
    AppError,
    ValidationError,
    TaskError,
    UserError,
    AuthError,
    FileError,
    DatabaseError,
    NetworkError,
    ConfigError,
    ErrorHandler,
    ErrorFactory
};
