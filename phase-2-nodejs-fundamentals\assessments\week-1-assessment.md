# Week 1 Assessment: Node.js Architecture & Core Modules

## Instructions
This assessment evaluates your understanding of Node.js fundamentals from Week 1. Complete all sections to demonstrate your mastery of Node.js architecture and core modules.

**Assessment Date:** [Enter date]
**Student Name:** [Enter your name]
**Week 1 Completion Date:** [Enter completion date]

---

## Part 1: Multiple Choice Questions (25 points)

### Node.js Architecture
1. What is the primary characteristic of Node.js architecture?
   a) Multi-threaded with shared memory
   b) Single-threaded with event loop
   c) Multi-process with message passing
   d) Synchronous execution model

2. Which phase of the event loop handles setTimeout callbacks?
   a) Timer phase
   b) Poll phase
   c) Check phase
   d) Close callbacks phase

3. What has the highest priority in the event loop?
   a) setTimeout callbacks
   b) setImmediate callbacks
   c) process.nextTick callbacks
   d) Promise callbacks

### Module Systems
4. Which is the traditional Node.js module system?
   a) ES Modules
   b) CommonJS
   c) AMD
   d) UMD

5. How do you export a function in CommonJS?
   a) export function myFunc() {}
   b) module.exports.myFunc = function() {}
   c) exports default function myFunc() {}
   d) return function myFunc() {}

### File System
6. Which method should you use for non-blocking file operations?
   a) fs.readFileSync()
   b) fs.readFile()
   c) fs.openSync()
   d) fs.writeFileSync()

7. What happens when you try to read a non-existent file asynchronously?
   a) The program crashes
   b) An error is passed to the callback
   c) undefined is returned
   d) An empty string is returned

### Core Modules
8. Which module is used for cross-platform path manipulation?
   a) os
   b) path
   c) fs
   d) url

9. What does process.env contain?
   a) Command line arguments
   b) Environment variables
   c) Process information
   d) System resources

10. Which crypto method is best for password hashing?
    a) crypto.createHash()
    b) crypto.randomBytes()
    c) crypto.pbkdf2()
    d) crypto.createCipher()

**Your Answers:** 1.___ 2.___ 3.___ 4.___ 5.___ 6.___ 7.___ 8.___ 9.___ 10.___

---

## Part 2: Short Answer Questions (25 points)

### Question 1 (5 points)
Explain the difference between blocking and non-blocking I/O operations in Node.js. Provide an example of each.

**Your Answer:**
```
[Write your explanation here]

Blocking example:
[Provide code example]

Non-blocking example:
[Provide code example]
```

### Question 2 (5 points)
Describe the event loop phases and explain why understanding them is important for Node.js developers.

**Your Answer:**
```
[Write your explanation of event loop phases]
```

### Question 3 (5 points)
What are the differences between CommonJS and ES modules? When would you use each?

**Your Answer:**
```
[Compare CommonJS and ES modules]
```

### Question 4 (5 points)
Explain how environment variables work in Node.js and why they are important for application configuration.

**Your Answer:**
```
[Explain environment variables usage]
```

### Question 5 (5 points)
Describe the purpose of the path module and provide examples of when you would use it.

**Your Answer:**
```
[Explain path module and provide examples]
```

---

## Part 3: Code Analysis and Debugging (25 points)

### Problem 1: Event Loop Order (10 points)
Predict the output order of this code and explain your reasoning:

```javascript
console.log('Start');

setTimeout(() => console.log('Timer 1'), 0);
setImmediate(() => console.log('Immediate 1'));

process.nextTick(() => {
    console.log('NextTick 1');
    process.nextTick(() => console.log('NextTick 2'));
});

Promise.resolve().then(() => {
    console.log('Promise 1');
    return Promise.resolve();
}).then(() => console.log('Promise 2'));

setTimeout(() => console.log('Timer 2'), 0);
setImmediate(() => console.log('Immediate 2'));

console.log('End');
```

**Your Answer:**
```
Output Order:
[List the order of console.log outputs]

Explanation:
[Explain why this order occurs based on event loop phases]
```

### Problem 2: Module System Error (8 points)
Identify and fix the errors in this module code:

```javascript
// math.js
function add(a, b) {
    return a + b;
}

function multiply(a, b) {
    return a * b;
}

export { add, multiply };

// app.js
const math = require('./math');
console.log(math.add(5, 3));
```

**Your Answer:**
```
Errors identified:
[List the errors]

Fixed code:
[Provide corrected code]
```

### Problem 3: File Operation Error Handling (7 points)
Improve this code to handle errors properly:

```javascript
const fs = require('fs');

fs.readFile('data.txt', 'utf8', (err, data) => {
    const parsed = JSON.parse(data);
    console.log(parsed.users.length);
});
```

**Your Answer:**
```javascript
// Improved code with proper error handling
[Write your improved version]
```

---

## Part 4: Practical Implementation (25 points)

### Task: Build a Configuration Manager
Create a Node.js module that manages application configuration with the following requirements:

#### Requirements:
1. **Environment Support**: Load different configs for development/production
2. **File Loading**: Read configuration from JSON files
3. **Environment Variables**: Override config with environment variables
4. **Validation**: Validate required configuration fields
5. **Error Handling**: Proper error handling for missing files/invalid data

#### Implementation:
```javascript
// config-manager.js
// Your implementation here

class ConfigManager {
    constructor() {
        // Initialize configuration manager
    }
    
    loadConfig(environment = 'development') {
        // Load configuration for specified environment
    }
    
    get(key, defaultValue = null) {
        // Get configuration value by key
    }
    
    validate() {
        // Validate required configuration fields
    }
}

module.exports = ConfigManager;

// Usage example:
// const ConfigManager = require('./config-manager');
// const config = new ConfigManager();
// config.loadConfig(process.env.NODE_ENV);
// const port = config.get('port', 3000);
```

**Your Implementation:**
```javascript
[Write your complete ConfigManager implementation]
```

#### Test Cases:
Create test cases that demonstrate your ConfigManager works correctly:

```javascript
// test-config-manager.js
// Your test cases here

[Write test cases that verify your implementation]
```

---

## Part 5: System Design Question (10 points)

### Question: CLI Application Architecture
Design the architecture for a command-line task management application using Node.js. Your design should include:

1. **Module Structure**: How you would organize the code into modules
2. **Data Storage**: How tasks would be stored and retrieved
3. **Configuration**: How the application would handle configuration
4. **Error Handling**: How errors would be managed throughout the application
5. **User Interface**: How the CLI interface would work

**Your Design:**
```
Module Structure:
[Describe your module organization]

Data Storage Strategy:
[Explain how you would handle data persistence]

Configuration Management:
[Describe configuration approach]

Error Handling Strategy:
[Explain error handling throughout the application]

CLI Interface Design:
[Describe user interaction patterns]

File Structure:
[Show the directory/file structure you would use]
```

---

## Part 6: Reflection and Analysis (10 points)

### Learning Reflection
1. **Most Challenging Concept**: What was the most difficult concept to understand this week?

**Your Answer:**
```
[Describe the challenging concept and how you overcame it]
```

2. **Practical Applications**: How would you apply this week's concepts in a real-world project?

**Your Answer:**
```
[Describe practical applications]
```

3. **Code Quality**: What practices did you learn for writing better Node.js code?

**Your Answer:**
```
[List code quality practices]
```

4. **Next Steps**: What areas do you want to explore further?

**Your Answer:**
```
[Describe areas for further learning]
```

---

## Assessment Summary

### Self-Evaluation Checklist
Mark your confidence level (1-5) for each area:

**Node.js Architecture:**
- [ ] Event loop understanding: ___/5
- [ ] Blocking vs non-blocking I/O: ___/5
- [ ] Process and environment: ___/5

**Module Systems:**
- [ ] CommonJS modules: ___/5
- [ ] ES modules: ___/5
- [ ] NPM package management: ___/5

**Core Modules:**
- [ ] File system operations: ___/5
- [ ] Path manipulation: ___/5
- [ ] Crypto operations: ___/5

**Practical Skills:**
- [ ] CLI application development: ___/5
- [ ] Error handling: ___/5
- [ ] Code organization: ___/5

### Overall Assessment
- **Total Study Time This Week:** ___ hours
- **Confidence Level:** ___/5
- **Ready for Week 2:** Yes / No
- **Areas Needing Review:** [List any areas]

---

## Scoring Guide

**Total Points: 120**

- **108-120 points (90%+)**: Excellent mastery - Ready for Week 2
- **96-107 points (80-89%)**: Good understanding - Minor review needed
- **84-95 points (70-79%)**: Satisfactory - Some concepts need reinforcement
- **Below 84 points (<70%)**: Need additional study before Week 2

---

**Congratulations on completing Week 1!** 🎉

You've built a solid foundation in Node.js architecture and core modules. Whether you're ready for Week 2 or need some additional practice, you've made significant progress in your backend development journey!
