# Week 3 Assessment: Asynchronous JavaScript

## Instructions
Complete this assessment to evaluate your understanding of Week 3 concepts. Answer all questions and complete all coding exercises. This assessment should take 60-90 minutes.

**Assessment Date:** [Enter date]
**Student Name:** [Enter your name]

---

## Part 1: Multiple Choice Questions (20 points)

### Question 1 (2 points)
What is the JavaScript event loop responsible for?
a) Executing synchronous code only
b) Managing asynchronous operations and callbacks
c) Creating new threads
d) Handling memory allocation

**Your Answer:** ___

### Question 2 (2 points)
Which Promise state indicates a successful operation?
a) pending
b) fulfilled
c) rejected
d) resolved

**Your Answer:** ___

### Question 3 (2 points)
What will this code output?
```javascript
console.log('1');
setTimeout(() => console.log('2'), 0);
console.log('3');
```
a) 1, 2, 3
b) 1, 3, 2
c) 2, 1, 3
d) 3, 1, 2

**Your Answer:** ___

### Question 4 (2 points)
Which method is used to handle Promise rejections?
a) `.then()`
b) `.catch()`
c) `.finally()`
d) `.reject()`

**Your Answer:** ___

### Question 5 (2 points)
What does `Promise.all()` do?
a) Executes promises sequentially
b) Returns the first resolved promise
c) Waits for all promises to resolve or any to reject
d) Creates a new promise

**Your Answer:** ___

### Question 6 (2 points)
Which keyword is used to wait for a Promise in an async function?
a) wait
b) await
c) async
d) promise

**Your Answer:** ___

### Question 7 (2 points)
What happens if you don't use `await` with a Promise in an async function?
a) The function throws an error
b) The Promise is ignored
c) The function returns a Promise
d) The function waits automatically

**Your Answer:** ___

### Question 8 (2 points)
How do you handle errors in async/await?
a) `.catch()` method
b) `try/catch` blocks
c) `error` callback
d) `finally` block

**Your Answer:** ___

### Question 9 (2 points)
What does `Promise.race()` return?
a) All resolved promises
b) The first promise to settle (resolve or reject)
c) The fastest promise to resolve
d) An array of all promises

**Your Answer:** ___

### Question 10 (2 points)
Which is true about callback hell?
a) It improves code readability
b) It makes error handling easier
c) It creates deeply nested callback functions
d) It's the preferred async pattern

**Your Answer:** ___

---

## Part 2: Short Answer Questions (20 points)

### Question 11 (4 points)
Explain the JavaScript event loop and how it handles asynchronous operations.

**Your Answer:**
```
[Write your explanation here]
```

### Question 12 (4 points)
What are the three states of a Promise? Describe each state.

**Your Answer:**
```
[Write your answer here]
```

### Question 13 (4 points)
Compare and contrast callbacks, Promises, and async/await. When would you use each?

**Your Answer:**
```
[Write your answer here]
```

### Question 14 (4 points)
Explain the difference between `Promise.all()` and `Promise.race()` with examples.

**Your Answer:**
```
[Write your answer here]
```

### Question 15 (4 points)
How do you handle errors in asynchronous JavaScript? Provide examples for each pattern.

**Your Answer:**
```
[Write your answer here]
```

---

## Part 3: Code Reading and Analysis (20 points)

### Question 16 (5 points)
What will this code output and in what order?

```javascript
console.log('Start');

setTimeout(() => {
    console.log('Timeout 1');
}, 0);

Promise.resolve().then(() => {
    console.log('Promise 1');
});

setTimeout(() => {
    console.log('Timeout 2');
}, 0);

console.log('End');
```

**Your Answer:**
```
Output:
[Write the output in order]

Explanation:
[Explain why this order occurs]
```

### Question 17 (5 points)
Analyze this Promise chain and identify any issues:

```javascript
function fetchData() {
    return fetch('/api/data')
        .then(response => response.json())
        .then(data => {
            console.log(data);
            return processData(data);
        })
        .catch(error => {
            console.error('Error:', error);
        });
}
```

**Your Answer:**
```
Issues identified:
[List any problems]

Improvements:
[Suggest improvements]
```

### Question 18 (5 points)
Convert this callback-based function to use Promises:

```javascript
function getData(callback) {
    setTimeout(() => {
        const data = { id: 1, name: 'Test' };
        callback(null, data);
    }, 1000);
}
```

**Your Answer:**
```javascript
// Promise-based version:
[Write your Promise-based implementation]
```

### Question 19 (5 points)
What's wrong with this async/await code?

```javascript
async function processItems(items) {
    const results = [];
    for (let item of items) {
        const result = await processItem(item);
        results.push(result);
    }
    return results;
}
```

**Your Answer:**
```
Problem:
[Identify the issue]

Better approach:
[Provide a better solution]
```

---

## Part 4: Coding Exercises (40 points)

### Exercise 1: Promise Creation (10 points)
Create a Promise-based function that simulates a network request with random success/failure.

```javascript
// Your code here:
function simulateNetworkRequest(url, delay = 1000) {
    // Create a Promise that:
    // - Resolves with mock data after the delay if successful
    // - Rejects with an error if unsuccessful (30% chance)
    // - Include the URL in the response/error
}

// Test your function:
simulateNetworkRequest('/api/users')
    .then(data => console.log('Success:', data))
    .catch(error => console.error('Error:', error));
```

### Exercise 2: Promise Chaining (10 points)
Create a series of Promise-based functions that chain together to process user data.

```javascript
// Your code here:
function fetchUser(id) {
    // Return a Promise that resolves with user data
}

function fetchUserPosts(userId) {
    // Return a Promise that resolves with user's posts
}

function processUserData(user, posts) {
    // Process and combine user and posts data
    // Return processed data
}

// Chain them together:
// fetchUser(1) -> fetchUserPosts(userId) -> processUserData(user, posts)
// Handle all errors appropriately
```

### Exercise 3: Async/Await Implementation (10 points)
Convert the Promise chain from Exercise 2 to use async/await with proper error handling.

```javascript
// Your code here:
async function getUserProfile(userId) {
    // Use async/await to:
    // 1. Fetch user data
    // 2. Fetch user posts
    // 3. Process and return combined data
    // 4. Handle errors with try/catch
}

// Test your function:
getUserProfile(1)
    .then(profile => console.log('Profile:', profile))
    .catch(error => console.error('Error:', error));
```

### Exercise 4: Promise Utilities (10 points)
Implement utility functions using Promise methods.

```javascript
// Your code here:

// 1. Create a function that fetches data from multiple URLs in parallel
async function fetchMultipleUrls(urls) {
    // Use Promise.all() to fetch all URLs simultaneously
    // Return array of results
}

// 2. Create a function with timeout functionality
function fetchWithTimeout(url, timeout = 5000) {
    // Use Promise.race() to implement timeout
    // Reject if request takes longer than timeout
}

// 3. Create a retry function
async function fetchWithRetry(url, maxRetries = 3) {
    // Retry failed requests up to maxRetries times
    // Use exponential backoff (delay increases each retry)
}

// Test your functions:
const urls = ['/api/users', '/api/posts', '/api/comments'];
fetchMultipleUrls(urls).then(console.log);
```

---

## Part 5: Problem Solving (Bonus - 10 points)

### Bonus Challenge
Create a Promise-based task queue that processes tasks sequentially with concurrency control.

```javascript
// Your code here:
class TaskQueue {
    constructor(concurrency = 1) {
        // Initialize queue with concurrency limit
    }
    
    add(task) {
        // Add task to queue
        // Return Promise that resolves when task completes
    }
    
    async process() {
        // Process tasks with concurrency control
    }
}

// Test your implementation:
const queue = new TaskQueue(2); // Max 2 concurrent tasks

// Add tasks
queue.add(() => simulateNetworkRequest('/api/task1'));
queue.add(() => simulateNetworkRequest('/api/task2'));
queue.add(() => simulateNetworkRequest('/api/task3'));

// Process all tasks
queue.process().then(() => console.log('All tasks completed'));
```

---

## Self-Evaluation

### Confidence Level (1-5 scale)
Rate your confidence in each area:

- Event loop understanding: ___/5
- Callback functions: ___/5
- Promise creation and chaining: ___/5
- Promise methods (all, race, etc.): ___/5
- Async/await syntax: ___/5
- Error handling in async code: ___/5

### Time Spent
- Total time on assessment: ___ minutes
- Most challenging part: _______________
- Easiest part: _______________

### Reflection
What async concepts do you need to review more?
```
[Write your reflection here]
```

What are you most proud of in this assessment?
```
[Write your reflection here]
```

---

## Answer Key (For Self-Checking)

### Part 1 Answers:
1. b) Managing asynchronous operations and callbacks
2. b) fulfilled
3. b) 1, 3, 2
4. b) `.catch()`
5. c) Waits for all promises to resolve or any to reject
6. b) await
7. c) The function returns a Promise
8. b) `try/catch` blocks
9. b) The first promise to settle (resolve or reject)
10. c) It creates deeply nested callback functions

### Scoring Guide:
- **90-100 points**: Excellent understanding - Ready for Week 4
- **80-89 points**: Good understanding - Review weak areas
- **70-79 points**: Satisfactory - Need more practice
- **Below 70 points**: Need to review Week 3 materials

---

**Assessment Complete!** 
Review your answers and identify areas for improvement before moving to Week 4.
