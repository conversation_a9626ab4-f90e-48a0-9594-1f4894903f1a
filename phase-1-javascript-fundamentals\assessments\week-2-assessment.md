# Week 2 Assessment: Functions, Scope & Closures

## Instructions
Complete this assessment to evaluate your understanding of Week 2 concepts. Answer all questions and complete all coding exercises. This assessment should take 60-90 minutes.

**Assessment Date:** [Enter date]
**Student Name:** [Enter your name]

---

## Part 1: Multiple Choice Questions (20 points)

### Question 1 (2 points)
Which of the following function declarations is hoisted?
a) `const func = function() {}`
b) `let func = () => {}`
c) `function func() {}`
d) `var func = () => {}`

**Your Answer:** ___

### Question 2 (2 points)
What will `console.log(x)` output in this code?
```javascript
function outer() {
    var x = 1;
    function inner() {
        var x = 2;
        console.log(x);
    }
    inner();
}
outer();
```
a) 1
b) 2
c) undefined
d) Error

**Your Answer:** ___

### Question 3 (2 points)
Which statement about arrow functions is true?
a) They have their own `this` binding
b) They can be used as constructors
c) They inherit `this` from the enclosing scope
d) They are always hoisted

**Your Answer:** ___

### Question 4 (2 points)
What is a closure?
a) A function that returns another function
b) A function that has access to variables in its outer scope
c) A function with no parameters
d) A function that calls itself

**Your Answer:** ___

### Question 5 (2 points)
What will this code output?
```javascript
for (var i = 0; i < 3; i++) {
    setTimeout(() => console.log(i), 100);
}
```
a) 0, 1, 2
b) 3, 3, 3
c) undefined, undefined, undefined
d) Error

**Your Answer:** ___

### Question 6 (2 points)
Which method can change the `this` context of a function?
a) `call()`
b) `apply()`
c) `bind()`
d) All of the above

**Your Answer:** ___

### Question 7 (2 points)
What is the difference between `call()` and `apply()`?
a) `call()` accepts arguments individually, `apply()` accepts an array
b) `apply()` accepts arguments individually, `call()` accepts an array
c) There is no difference
d) `call()` is for arrow functions, `apply()` is for regular functions

**Your Answer:** ___

### Question 8 (2 points)
What will `func()` return?
```javascript
function createMultiplier(x) {
    return function(y) {
        return x * y;
    };
}
const func = createMultiplier(3);
```
a) 3
b) A function
c) undefined
d) Error

**Your Answer:** ___

### Question 9 (2 points)
Which scope does `let` and `const` have?
a) Function scope
b) Global scope
c) Block scope
d) Module scope

**Your Answer:** ___

### Question 10 (2 points)
What is a higher-order function?
a) A function with many parameters
b) A function that takes or returns other functions
c) A function with complex logic
d) A function that uses closures

**Your Answer:** ___

---

## Part 2: Short Answer Questions (20 points)

### Question 11 (4 points)
Explain the difference between function declarations and function expressions, including their hoisting behavior.

**Your Answer:**
```
[Write your explanation here]
```

### Question 12 (4 points)
What is the scope chain? How does JavaScript resolve variable names?

**Your Answer:**
```
[Write your answer here]
```

### Question 13 (4 points)
Explain how closures work and provide a practical use case.

**Your Answer:**
```
[Write your answer here]
```

### Question 14 (4 points)
How does `this` binding work in regular functions vs arrow functions?

**Your Answer:**
```
[Write your answer here]
```

### Question 15 (4 points)
What are the benefits of using higher-order functions? Give two examples.

**Your Answer:**
```
[Write your answer here]
```

---

## Part 3: Code Reading and Analysis (20 points)

### Question 16 (5 points)
What will this code output and why?

```javascript
function test() {
    console.log(a);
    console.log(b);
    var a = 1;
    let b = 2;
}
test();
```

**Your Answer:**
```
Output:
[Write the output here]

Explanation:
[Explain why this output occurs]
```

### Question 17 (5 points)
Analyze this closure example and explain what happens:

```javascript
function createCounter() {
    let count = 0;
    return {
        increment: () => ++count,
        decrement: () => --count,
        getValue: () => count
    };
}

const counter1 = createCounter();
const counter2 = createCounter();
console.log(counter1.increment());
console.log(counter2.increment());
console.log(counter1.getValue());
```

**Your Answer:**
```
Output:
[Write the output here]

Explanation:
[Explain how closures work in this example]
```

### Question 18 (5 points)
What's wrong with this code and how would you fix it?

```javascript
const obj = {
    name: "Test",
    greet: () => {
        console.log(`Hello, ${this.name}`);
    }
};
obj.greet();
```

**Your Answer:**
```javascript
// Problem:
[Explain the issue]

// Fixed code:
[Write the corrected code here]
```

### Question 19 (5 points)
Predict the output and explain the `this` binding:

```javascript
const person = {
    name: "Alice",
    greet: function() {
        console.log(`Hello, ${this.name}`);
    }
};

const greetFunc = person.greet;
greetFunc();
person.greet();
```

**Your Answer:**
```
Output:
[Write the output here]

Explanation:
[Explain the this binding in each case]
```

---

## Part 4: Coding Exercises (40 points)

### Exercise 1: Function Types (10 points)
Create three versions of the same function using different declaration types:
1. Function declaration
2. Function expression
3. Arrow function

The function should calculate the area of a rectangle and handle invalid inputs.

```javascript
// Your code here:

// 1. Function declaration
function calculateAreaDeclaration(length, width) {
    // Your implementation
}

// 2. Function expression
const calculateAreaExpression = function(length, width) {
    // Your implementation
};

// 3. Arrow function
const calculateAreaArrow = (length, width) => {
    // Your implementation
};

// Test all three functions
console.log(calculateAreaDeclaration(5, 3));
console.log(calculateAreaExpression(5, 3));
console.log(calculateAreaArrow(5, 3));
```

### Exercise 2: Closure Practice (10 points)
Create a closure-based bank account system with private balance and transaction history.

```javascript
// Your code here:
function createBankAccount(initialBalance) {
    // Your implementation
    // Should return an object with methods:
    // - deposit(amount)
    // - withdraw(amount)
    // - getBalance()
    // - getTransactionHistory()
}

// Test your implementation:
const account = createBankAccount(100);
console.log(account.deposit(50));
console.log(account.withdraw(25));
console.log(account.getBalance());
console.log(account.getTransactionHistory());
```

### Exercise 3: Higher-Order Functions (10 points)
Create a function that takes an array of numbers and a callback function, then applies the callback to each element.

```javascript
// Your code here:
function processArray(numbers, callback) {
    // Your implementation
}

// Create callback functions
function double(x) {
    // Your implementation
}

function square(x) {
    // Your implementation
}

function isEven(x) {
    // Your implementation
}

// Test your functions:
const numbers = [1, 2, 3, 4, 5];
console.log(processArray(numbers, double));
console.log(processArray(numbers, square));
console.log(processArray(numbers, isEven));
```

### Exercise 4: Context Binding (10 points)
Create an object with methods that demonstrate proper `this` binding and use `call`, `apply`, and `bind`.

```javascript
// Your code here:
const calculator = {
    result: 0,
    
    add: function(a, b) {
        // Your implementation
    },
    
    multiply: function(a, b) {
        // Your implementation
    },
    
    getResult: function() {
        // Your implementation
    },
    
    reset: function() {
        // Your implementation
    }
};

// Demonstrate call, apply, and bind:
// Your demonstration code here
```

---

## Part 5: Problem Solving (Bonus - 10 points)

### Bonus Challenge
Create a memoization function that caches the results of expensive function calls.

```javascript
// Your code here:
function memoize(func) {
    // Your implementation
    // Should return a memoized version of the function
}

// Test function (expensive operation simulation)
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// Test your memoization:
const memoizedFib = memoize(fibonacci);
console.time('First call');
console.log(memoizedFib(40));
console.timeEnd('First call');

console.time('Second call (cached)');
console.log(memoizedFib(40));
console.timeEnd('Second call (cached)');
```

---

## Self-Evaluation

### Confidence Level (1-5 scale)
Rate your confidence in each area:

- Function declarations and expressions: ___/5
- Scope and scope chain: ___/5
- Closures: ___/5
- Higher-order functions: ___/5
- `this` binding: ___/5
- `call`, `apply`, `bind`: ___/5

### Time Spent
- Total time on assessment: ___ minutes
- Most challenging part: _______________
- Easiest part: _______________

### Reflection
What concepts do you need to review more?
```
[Write your reflection here]
```

What are you most proud of in this assessment?
```
[Write your reflection here]
```

---

## Answer Key (For Self-Checking)

### Part 1 Answers:
1. c) `function func() {}`
2. b) 2
3. c) They inherit `this` from the enclosing scope
4. b) A function that has access to variables in its outer scope
5. b) 3, 3, 3
6. d) All of the above
7. a) `call()` accepts arguments individually, `apply()` accepts an array
8. b) A function
9. c) Block scope
10. b) A function that takes or returns other functions

### Scoring Guide:
- **90-100 points**: Excellent understanding - Ready for Week 3
- **80-89 points**: Good understanding - Review weak areas
- **70-79 points**: Satisfactory - Need more practice
- **Below 70 points**: Need to review Week 2 materials

---

**Assessment Complete!** 
Review your answers and identify areas for improvement before moving to Week 3.
