# Week 4 Assessment: Modern JavaScript Features & Error Handling

## Instructions
Complete this assessment to evaluate your understanding of Week 4 concepts. Answer all questions and complete all coding exercises. This assessment should take 60-90 minutes.

**Assessment Date:** [Enter date]
**Student Name:** [Enter your name]

---

## Part 1: Multiple Choice Questions (20 points)

### Question 1 (2 points)
Which syntax correctly destructures an array?
a) `{a, b, c} = [1, 2, 3]`
b) `[a, b, c] = {1, 2, 3}`
c) `[a, b, c] = [1, 2, 3]`
d) `(a, b, c) = [1, 2, 3]`

**Your Answer:** ___

### Question 2 (2 points)
What does the spread operator (...) do with arrays?
a) Combines arrays into one
b) Spreads array elements as individual arguments
c) Creates a copy of an array
d) All of the above

**Your Answer:** ___

### Question 3 (2 points)
Which template literal syntax is correct?
a) `"Hello ${name}"`
b) `'Hello ${name}'`
c) `` `Hello ${name}` ``
d) `Hello ${name}`

**Your Answer:** ___

### Question 4 (2 points)
How do you create a class that extends another class?
a) `class Child inherits Parent {}`
b) `class Child extends Parent {}`
c) `class Child : Parent {}`
d) `class Child from Parent {}`

**Your Answer:** ___

### Question 5 (2 points)
What is the purpose of the `super()` keyword?
a) To create a new instance
b) To call the parent class constructor
c) To define static methods
d) To create private properties

**Your Answer:** ___

### Question 6 (2 points)
Which export syntax is correct for named exports?
a) `export default function myFunc() {}`
b) `export { myFunc }`
c) `module.exports = myFunc`
d) `exports.myFunc = myFunc`

**Your Answer:** ___

### Question 7 (2 points)
How do you import a default export?
a) `import { default } from './module'`
b) `import default from './module'`
c) `import myModule from './module'`
d) `import * as myModule from './module'`

**Your Answer:** ___

### Question 8 (2 points)
Which block is guaranteed to execute in try/catch/finally?
a) try
b) catch
c) finally
d) None of them

**Your Answer:** ___

### Question 9 (2 points)
How do you create a custom error class?
a) `class MyError extends Error {}`
b) `class MyError inherits Error {}`
c) `function MyError() extends Error {}`
d) `const MyError = Error.create()`

**Your Answer:** ___

### Question 10 (2 points)
What happens if you don't handle a Promise rejection?
a) The program crashes
b) It's silently ignored
c) An unhandled rejection warning is shown
d) It automatically retries

**Your Answer:** ___

---

## Part 2: Short Answer Questions (20 points)

### Question 11 (4 points)
Explain the difference between destructuring assignment and the spread operator. Provide examples.

**Your Answer:**
```
[Write your explanation here]
```

### Question 12 (4 points)
What are the advantages of using ES6 classes over function constructors?

**Your Answer:**
```
[Write your answer here]
```

### Question 13 (4 points)
Explain the difference between named exports and default exports in ES6 modules.

**Your Answer:**
```
[Write your answer here]
```

### Question 14 (4 points)
Describe a comprehensive error handling strategy for a JavaScript application.

**Your Answer:**
```
[Write your answer here]
```

### Question 15 (4 points)
How do template literals improve string handling compared to traditional string concatenation?

**Your Answer:**
```
[Write your answer here]
```

---

## Part 3: Code Reading and Analysis (20 points)

### Question 16 (5 points)
What will this destructuring code output?

```javascript
const user = {
    name: 'John',
    age: 30,
    address: {
        city: 'New York',
        country: 'USA'
    }
};

const {
    name,
    age: userAge,
    address: { city },
    occupation = 'Unknown'
} = user;

console.log(name, userAge, city, occupation);
```

**Your Answer:**
```
Output:
[Write the output]

Explanation:
[Explain the destructuring process]
```

### Question 17 (5 points)
Analyze this class inheritance example:

```javascript
class Animal {
    constructor(name) {
        this.name = name;
    }
    
    speak() {
        console.log(`${this.name} makes a sound`);
    }
}

class Dog extends Animal {
    constructor(name, breed) {
        super(name);
        this.breed = breed;
    }
    
    speak() {
        console.log(`${this.name} barks`);
    }
}

const dog = new Dog('Rex', 'German Shepherd');
dog.speak();
```

**Your Answer:**
```
Output:
[Write the output]

Explanation:
[Explain inheritance and method overriding]
```

### Question 18 (5 points)
What's wrong with this module structure?

```javascript
// math.js
export default function add(a, b) {
    return a + b;
}

export function subtract(a, b) {
    return a - b;
}

// main.js
import add, subtract from './math.js';
console.log(add(5, 3));
console.log(subtract(5, 3));
```

**Your Answer:**
```javascript
// Problem:
[Identify the issue]

// Corrected code:
[Write the corrected import statement]
```

### Question 19 (5 points)
Analyze this error handling code:

```javascript
async function processData(data) {
    try {
        const result = await validateData(data);
        const processed = await transformData(result);
        return processed;
    } catch (error) {
        console.error('Error:', error.message);
        throw error;
    } finally {
        console.log('Processing completed');
    }
}
```

**Your Answer:**
```
Analysis:
[Evaluate the error handling approach]

Improvements:
[Suggest any improvements]
```

---

## Part 4: Coding Exercises (40 points)

### Exercise 1: Destructuring and Spread (10 points)
Create functions that demonstrate advanced destructuring and spread operator usage.

```javascript
// Your code here:

// 1. Function that swaps object properties using destructuring
function swapProperties(obj, prop1, prop2) {
    // Swap the values of prop1 and prop2 in the object
    // Return the modified object
}

// 2. Function that merges multiple objects using spread
function mergeObjects(...objects) {
    // Merge all objects into one, with later objects overriding earlier ones
    // Return the merged object
}

// 3. Function that extracts specific array elements
function extractElements(arr, ...indices) {
    // Extract elements at specified indices using destructuring
    // Return array of extracted elements
}

// Test your functions:
const obj = { a: 1, b: 2, c: 3 };
console.log(swapProperties(obj, 'a', 'b'));

const merged = mergeObjects({a: 1}, {b: 2}, {a: 3, c: 4});
console.log(merged);

const arr = [10, 20, 30, 40, 50];
console.log(extractElements(arr, 0, 2, 4));
```

### Exercise 2: ES6 Classes (10 points)
Create a class hierarchy for a simple inventory system.

```javascript
// Your code here:

// Base class
class Product {
    constructor(id, name, price) {
        // Initialize properties
    }
    
    getInfo() {
        // Return product information string
    }
    
    static comparePrice(product1, product2) {
        // Static method to compare prices
    }
}

// Derived class
class Electronics extends Product {
    constructor(id, name, price, warranty) {
        // Call parent constructor and initialize warranty
    }
    
    getInfo() {
        // Override to include warranty information
    }
    
    extendWarranty(months) {
        // Method to extend warranty
    }
}

// Test your classes:
const laptop = new Electronics(1, 'Laptop', 999, 12);
const phone = new Electronics(2, 'Phone', 599, 24);

console.log(laptop.getInfo());
console.log(Product.comparePrice(laptop, phone));
```

### Exercise 3: Module System (10 points)
Create a modular calculator system with proper imports/exports.

```javascript
// Your code here:

// File: calculator.js (simulate with comments)
// Create a module that exports:
// - Named exports: add, subtract, multiply, divide functions
// - Default export: Calculator class with history tracking

// File: math-utils.js (simulate with comments)  
// Create utility functions:
// - Named exports: isEven, isPrime, factorial
// - Default export: MathUtils class

// File: main.js (simulate with comments)
// Import and use the calculator and utilities
// Demonstrate different import syntaxes

// Write the actual code for one of the modules:
class Calculator {
    constructor() {
        this.history = [];
    }
    
    add(a, b) {
        // Implementation with history tracking
    }
    
    getHistory() {
        // Return calculation history
    }
}

// Export statements
// export { add, subtract, multiply, divide };
// export default Calculator;
```

### Exercise 4: Error Handling (10 points)
Create a robust error handling system with custom errors.

```javascript
// Your code here:

// 1. Create custom error classes
class ValidationError extends Error {
    constructor(message, field) {
        // Initialize with message and field information
    }
}

class NetworkError extends Error {
    constructor(message, statusCode) {
        // Initialize with message and status code
    }
}

// 2. Create a function with comprehensive error handling
async function processUserData(userData) {
    try {
        // Validate user data (throw ValidationError if invalid)
        // Simulate network request (throw NetworkError if fails)
        // Process and return data
    } catch (error) {
        // Handle different error types appropriately
        // Log errors and re-throw or return appropriate response
    } finally {
        // Cleanup operations
    }
}

// 3. Create an error handler utility
function handleError(error) {
    // Handle different types of errors
    // Return user-friendly error messages
    // Log technical details for debugging
}

// Test your error handling:
processUserData({ name: '', email: 'invalid' })
    .then(result => console.log('Success:', result))
    .catch(error => console.log('Error:', handleError(error)));
```

---

## Part 5: Problem Solving (Bonus - 10 points)

### Bonus Challenge
Create a modern JavaScript utility library that demonstrates all Week 4 concepts.

```javascript
// Your code here:
class UtilityLibrary {
    // Use private fields (modern syntax)
    #version = '1.0.0';
    
    constructor(config = {}) {
        // Destructure config with defaults
        // Initialize library
    }
    
    // Template literal method for formatting
    format(template, ...values) {
        // Use template literals for string formatting
    }
    
    // Async method with error handling
    async fetchAndProcess(url, processor) {
        // Fetch data and apply processor function
        // Handle all possible errors
    }
    
    // Method using spread and destructuring
    processData({ data, options = {}, ...metadata }) {
        // Process data with options and metadata
    }
    
    // Static factory method
    static create(config) {
        // Create and return new instance
    }
}

// Export the library
// export default UtilityLibrary;

// Demonstrate usage:
const utils = UtilityLibrary.create({
    debug: true,
    timeout: 5000
});
```

---

## Self-Evaluation

### Confidence Level (1-5 scale)
Rate your confidence in each area:

- Destructuring assignment: ___/5
- Spread and rest operators: ___/5
- Template literals: ___/5
- ES6 classes and inheritance: ___/5
- ES6 modules: ___/5
- Error handling: ___/5

### Time Spent
- Total time on assessment: ___ minutes
- Most challenging part: _______________
- Easiest part: _______________

### Reflection
What modern JavaScript concepts do you need to review more?
```
[Write your reflection here]
```

What are you most excited to apply in real projects?
```
[Write your reflection here]
```

---

## Answer Key (For Self-Checking)

### Part 1 Answers:
1. c) `[a, b, c] = [1, 2, 3]`
2. d) All of the above
3. c) `` `Hello ${name}` ``
4. b) `class Child extends Parent {}`
5. b) To call the parent class constructor
6. b) `export { myFunc }`
7. c) `import myModule from './module'`
8. c) finally
9. a) `class MyError extends Error {}`
10. c) An unhandled rejection warning is shown

### Scoring Guide:
- **90-100 points**: Excellent understanding - Ready for Final Project
- **80-89 points**: Good understanding - Review weak areas
- **70-79 points**: Satisfactory - Need more practice
- **Below 70 points**: Need to review Week 4 materials

---

**Assessment Complete!** 
Review your answers and identify areas for improvement before starting the Final Project.
